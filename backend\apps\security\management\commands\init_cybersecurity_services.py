from django.core.management.base import BaseCommand
from apps.security.models import CyberSecurityService


class Command(BaseCommand):
    help = 'Initialize cybersecurity services with current TrustVault setup'

    def handle(self, *args, **options):
        """Initialize cybersecurity services."""
        
        services_data = [
            {
                'name': 'Suricata IDS',
                'service_type': 'IDS',
                'description': 'Network-based intrusion detection system monitoring all traffic',
                'endpoint_url': '',
                'status': 'ACTIVE',
                'is_monitoring_enabled': True,
            },
            {
                'name': 'Fail2Ban IPS',
                'service_type': 'IPS',
                'description': 'Intrusion prevention system with automatic IP blocking',
                'endpoint_url': '',
                'status': 'ACTIVE',
                'is_monitoring_enabled': True,
            },
            {
                'name': 'Wazuh SIEM',
                'service_type': 'SIEM',
                'description': 'Security information and event management system',
                'endpoint_url': 'http://localhost:5601',
                'status': 'ACTIVE',
                'is_monitoring_enabled': True,
            },
            {
                'name': 'Prometheus Metrics',
                'service_type': 'MONITORING',
                'description': 'Real-time metrics collection and monitoring',
                'endpoint_url': 'http://localhost:9090',
                'status': 'ACTIVE',
                'is_monitoring_enabled': True,
            },
            {
                'name': 'Grafana Dashboard',
                'service_type': 'MONITORING',
                'description': 'Security monitoring and visualization dashboard',
                'endpoint_url': 'http://localhost:3001',
                'status': 'ACTIVE',
                'is_monitoring_enabled': True,
            },
            {
                'name': 'AlertManager',
                'service_type': 'ALERTING',
                'description': 'Alert routing and notification management',
                'endpoint_url': 'http://localhost:9093',
                'status': 'ACTIVE',
                'is_monitoring_enabled': True,
            },
            {
                'name': 'Nginx WAF',
                'service_type': 'WAF',
                'description': 'Web application firewall with security headers',
                'endpoint_url': 'http://localhost',
                'status': 'ACTIVE',
                'is_monitoring_enabled': True,
            },
        ]
        
        created_count = 0
        updated_count = 0
        
        for service_data in services_data:
            service, created = CyberSecurityService.objects.get_or_create(
                name=service_data['name'],
                defaults=service_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created service: {service.name}')
                )
            else:
                # Update existing service
                for key, value in service_data.items():
                    if key != 'name':
                        setattr(service, key, value)
                service.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Updated service: {service.name}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\nInitialization complete!\n'
                f'Created: {created_count} services\n'
                f'Updated: {updated_count} services\n'
                f'Total: {CyberSecurityService.objects.count()} services'
            )
        )
