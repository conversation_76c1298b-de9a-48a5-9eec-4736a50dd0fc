import React from 'react';
import {
  Box,
  Container,
  Typography,
  Breadcrumbs,
  Link,
  Chip
} from '@mui/material';
import {
  Security,
  Shield
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { CyberSecurityDashboard } from '../../components/CyberSecurity';

const CyberSecurityPage: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>Cybersecurity Management - TrustVault</title>
        <meta name="description" content="Comprehensive cybersecurity management dashboard for TrustVault" />
      </Helmet>

      <Container maxWidth="xl" sx={{ py: 3 }}>
        {/* Breadcrumbs */}
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          <Link color="inherit" href="/dashboard">
            Dashboard
          </Link>
          <Typography color="text.primary">Cybersecurity</Typography>
        </Breadcrumbs>

        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Box display="flex" alignItems="center" gap={2}>
            <Security color="primary" sx={{ fontSize: 32 }} />
            <Box>
              <Typography variant="h4" component="h1">
                Cybersecurity Management
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Monitor and manage all cybersecurity systems from one central dashboard
              </Typography>
            </Box>
          </Box>
          <Chip
            icon={<Shield />}
            label="Security Center"
            color="primary"
            variant="outlined"
          />
        </Box>

        {/* Comprehensive Cybersecurity Dashboard */}
        <CyberSecurityDashboard />
      </Container>
    </>
  );
};

export default CyberSecurityPage;
