<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrustVault - Wazuh SIEM Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            padding: 40px 0;
        }
        .header h1 {
            font-size: 3em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 1.2em;
            margin: 10px 0;
            opacity: 0.9;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card h3 {
            margin-top: 0;
            font-size: 1.5em;
            color: #fff;
        }
        .metric {
            font-size: 2.5em;
            font-weight: bold;
            color: #4CAF50;
            margin: 10px 0;
        }
        .status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        .status.active {
            background: #4CAF50;
            color: white;
        }
        .status.warning {
            background: #FF9800;
            color: white;
        }
        .status.error {
            background: #f44336;
            color: white;
        }
        .footer {
            text-align: center;
            padding: 40px 0;
            opacity: 0.8;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            margin: 10px;
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ TrustVault SIEM</h1>
            <p>Wazuh Security Information and Event Management</p>
            <p><span class="status active">OPERATIONAL</span></p>
        </div>

        <div class="dashboard-grid">
            <div class="card">
                <h3>📊 Security Events</h3>
                <div class="metric">1,247</div>
                <p>Events processed in the last 24 hours</p>
                <span class="status active">Normal</span>
            </div>

            <div class="card">
                <h3>🖥️ Monitored Agents</h3>
                <div class="metric">12</div>
                <p>Active agents reporting</p>
                <span class="status active">All Connected</span>
            </div>

            <div class="card">
                <h3>⚠️ Security Alerts</h3>
                <div class="metric">3</div>
                <p>Active security alerts</p>
                <span class="status warning">Review Required</span>
            </div>

            <div class="card">
                <h3>🔍 Threat Detection</h3>
                <div class="metric">0</div>
                <p>Critical threats detected</p>
                <span class="status active">Secure</span>
            </div>

            <div class="card">
                <h3>📈 System Health</h3>
                <div class="metric">98%</div>
                <p>Overall system performance</p>
                <span class="status active">Excellent</span>
            </div>

            <div class="card">
                <h3>🔐 Compliance Status</h3>
                <div class="metric">95%</div>
                <p>Compliance score</p>
                <span class="status active">Compliant</span>
            </div>
        </div>

        <div class="footer">
            <h3>🔧 Quick Actions</h3>
            <a href="/api/health" class="btn">📊 System Status</a>
            <a href="/" class="btn">🏠 Main Dashboard</a>
            <a href="/api/" class="btn">🔌 API Endpoints</a>
            
            <p style="margin-top: 40px;">
                <strong>TrustVault SIEM Dashboard</strong><br>
                Powered by Wazuh | Integrated Security Monitoring
            </p>
            
            <p style="font-size: 0.9em; opacity: 0.7;">
                This is a demonstration interface. The full Wazuh dashboard is being configured.<br>
                For production use, connect to the complete Wazuh management interface.
            </p>
        </div>
    </div>

    <script>
        // Simple auto-refresh for demo
        setInterval(() => {
            const metrics = document.querySelectorAll('.metric');
            metrics.forEach(metric => {
                if (metric.textContent.includes('%')) return;
                const current = parseInt(metric.textContent.replace(/,/g, ''));
                const variation = Math.floor(Math.random() * 10) - 5;
                const newValue = Math.max(0, current + variation);
                metric.textContent = newValue.toLocaleString();
            });
        }, 30000); // Update every 30 seconds
    </script>
</body>
</html>
