# Generated by Django 4.2.7 on 2025-08-03 15:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('security', '0002_complianceaudit_devicefingerprint_incidentresponse_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CyberSecurityService',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=100)),
                ('service_type', models.CharField(choices=[('IDS', 'Intrusion Detection System'), ('IPS', 'Intrusion Prevention System'), ('SIEM', 'Security Information and Event Management'), ('MONITORING', 'Security Monitoring'), ('ALERTING', 'Alert Management'), ('WAF', 'Web Application Firewall'), ('ANTIVIRUS', 'Antivirus Protection'), ('VULNERABILITY_SCANNER', 'Vulnerability Scanner'), ('THREAT_INTELLIGENCE', 'Threat Intelligence'), ('BACKUP', 'Backup System')], max_length=25)),
                ('description', models.TextField(blank=True)),
                ('endpoint_url', models.URLField(blank=True)),
                ('api_key', models.CharField(blank=True, max_length=255)),
                ('config_data', models.JSONField(blank=True, default=dict)),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('INACTIVE', 'Inactive'), ('MAINTENANCE', 'Under Maintenance'), ('ERROR', 'Error'), ('INITIALIZING', 'Initializing')], default='INACTIVE', max_length=15)),
                ('health_status', models.CharField(choices=[('HEALTHY', 'Healthy'), ('WARNING', 'Warning'), ('CRITICAL', 'Critical'), ('UNKNOWN', 'Unknown')], default='UNKNOWN', max_length=10)),
                ('last_health_check', models.DateTimeField(blank=True, null=True)),
                ('uptime_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('response_time_ms', models.IntegerField(default=0)),
                ('is_monitoring_enabled', models.BooleanField(default=True)),
                ('alert_threshold', models.JSONField(blank=True, default=dict)),
            ],
            options={
                'ordering': ['service_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ThreatDetection',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('threat_category', models.CharField(choices=[('MALWARE', 'Malware'), ('INTRUSION', 'Intrusion Attempt'), ('DDOS', 'DDoS Attack'), ('BRUTE_FORCE', 'Brute Force Attack'), ('SQL_INJECTION', 'SQL Injection'), ('XSS', 'Cross-Site Scripting'), ('PHISHING', 'Phishing Attempt'), ('SUSPICIOUS_ACTIVITY', 'Suspicious Activity'), ('POLICY_VIOLATION', 'Policy Violation'), ('ANOMALY', 'Anomaly Detection')], max_length=20)),
                ('severity', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('CRITICAL', 'Critical')], max_length=20)),
                ('confidence_score', models.DecimalField(decimal_places=2, max_digits=5)),
                ('source_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('source_port', models.IntegerField(blank=True, null=True)),
                ('target_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('target_port', models.IntegerField(blank=True, null=True)),
                ('attack_signature', models.TextField(blank=True)),
                ('payload', models.TextField(blank=True)),
                ('user_agent', models.TextField(blank=True)),
                ('action_taken', models.CharField(choices=[('BLOCKED', 'Blocked'), ('QUARANTINED', 'Quarantined'), ('LOGGED', 'Logged Only'), ('ALERTED', 'Alert Generated'), ('IGNORED', 'Ignored'), ('MANUAL_REVIEW', 'Manual Review Required')], max_length=15)),
                ('is_false_positive', models.BooleanField(default=False)),
                ('geolocation', models.JSONField(blank=True, default=dict)),
                ('threat_intelligence', models.JSONField(blank=True, default=dict)),
                ('raw_data', models.JSONField(blank=True, default=dict)),
                ('detecting_service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='security.cybersecurityservice')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SystemIntegration',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=100)),
                ('integration_type', models.CharField(choices=[('PROMETHEUS', 'Prometheus Metrics'), ('GRAFANA', 'Grafana Dashboard'), ('ALERTMANAGER', 'AlertManager'), ('SURICATA', 'Suricata IDS'), ('FAIL2BAN', 'Fail2Ban IPS'), ('WAZUH', 'Wazuh SIEM'), ('ELASTICSEARCH', 'Elasticsearch'), ('SPLUNK', 'Splunk'), ('SYSLOG', 'Syslog Server'), ('WEBHOOK', 'Webhook Integration')], max_length=20)),
                ('description', models.TextField(blank=True)),
                ('endpoint_url', models.URLField()),
                ('authentication', models.JSONField(blank=True, default=dict)),
                ('config_parameters', models.JSONField(blank=True, default=dict)),
                ('data_mapping', models.JSONField(blank=True, default=dict)),
                ('is_enabled', models.BooleanField(default=True)),
                ('last_sync', models.DateTimeField(blank=True, null=True)),
                ('sync_status', models.CharField(default='PENDING', max_length=20)),
                ('error_message', models.TextField(blank=True)),
                ('sync_frequency', models.IntegerField(default=300)),
                ('timeout_seconds', models.IntegerField(default=30)),
                ('retry_count', models.IntegerField(default=3)),
            ],
            options={
                'ordering': ['integration_type', 'name'],
                'indexes': [models.Index(fields=['integration_type', 'is_enabled'], name='security_sy_integra_c02600_idx'), models.Index(fields=['last_sync'], name='security_sy_last_sy_b6d57b_idx')],
            },
        ),
        migrations.CreateModel(
            name='SecuritySystemMetrics',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('cpu_usage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('memory_usage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('disk_usage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('network_io', models.JSONField(blank=True, default=dict)),
                ('events_processed', models.BigIntegerField(default=0)),
                ('threats_detected', models.IntegerField(default=0)),
                ('threats_blocked', models.IntegerField(default=0)),
                ('false_positives', models.IntegerField(default=0)),
                ('response_time', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True)),
                ('throughput', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('error_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('custom_metrics', models.JSONField(blank=True, default=dict)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='metrics', to='security.cybersecurityservice')),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='SecurityReport',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=200)),
                ('report_type', models.CharField(choices=[('DAILY_SUMMARY', 'Daily Security Summary'), ('WEEKLY_ANALYSIS', 'Weekly Security Analysis'), ('MONTHLY_COMPLIANCE', 'Monthly Compliance Report'), ('INCIDENT_REPORT', 'Incident Response Report'), ('THREAT_INTELLIGENCE', 'Threat Intelligence Report'), ('VULNERABILITY_ASSESSMENT', 'Vulnerability Assessment'), ('PENETRATION_TEST', 'Penetration Test Report'), ('CUSTOM', 'Custom Report')], max_length=25)),
                ('description', models.TextField(blank=True)),
                ('date_range_start', models.DateTimeField()),
                ('date_range_end', models.DateTimeField()),
                ('filters', models.JSONField(blank=True, default=dict)),
                ('status', models.CharField(choices=[('GENERATING', 'Generating'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('SCHEDULED', 'Scheduled')], default='SCHEDULED', max_length=15)),
                ('generated_at', models.DateTimeField(blank=True, null=True)),
                ('report_data', models.JSONField(blank=True, default=dict)),
                ('file_path', models.CharField(blank=True, max_length=500)),
                ('file_size', models.BigIntegerField(default=0)),
                ('is_scheduled', models.BooleanField(default=False)),
                ('schedule_frequency', models.CharField(blank=True, max_length=20)),
                ('next_generation', models.DateTimeField(blank=True, null=True)),
                ('generated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SecurityDashboard',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('dashboard_type', models.CharField(choices=[('OVERVIEW', 'Security Overview'), ('THREATS', 'Threat Analysis'), ('COMPLIANCE', 'Compliance Status'), ('PERFORMANCE', 'System Performance'), ('INCIDENTS', 'Incident Management'), ('CUSTOM', 'Custom Dashboard')], max_length=15)),
                ('description', models.TextField(blank=True)),
                ('widgets', models.JSONField(blank=True, default=list)),
                ('layout', models.JSONField(blank=True, default=dict)),
                ('refresh_interval', models.IntegerField(default=30)),
                ('is_public', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('auto_refresh', models.BooleanField(default=True)),
                ('allowed_users', models.ManyToManyField(blank=True, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['dashboard_type', 'name'],
            },
        ),
        migrations.AddIndex(
            model_name='cybersecurityservice',
            index=models.Index(fields=['service_type', 'status'], name='security_cy_service_98c4ff_idx'),
        ),
        migrations.AddIndex(
            model_name='cybersecurityservice',
            index=models.Index(fields=['health_status'], name='security_cy_health__7411c8_idx'),
        ),
        migrations.AddIndex(
            model_name='cybersecurityservice',
            index=models.Index(fields=['last_health_check'], name='security_cy_last_he_2875a3_idx'),
        ),
        migrations.AddIndex(
            model_name='threatdetection',
            index=models.Index(fields=['detecting_service', 'created_at'], name='security_th_detecti_2f90c0_idx'),
        ),
        migrations.AddIndex(
            model_name='threatdetection',
            index=models.Index(fields=['threat_category', 'severity'], name='security_th_threat__7ddf0c_idx'),
        ),
        migrations.AddIndex(
            model_name='threatdetection',
            index=models.Index(fields=['source_ip', 'created_at'], name='security_th_source__8bea95_idx'),
        ),
        migrations.AddIndex(
            model_name='threatdetection',
            index=models.Index(fields=['is_false_positive'], name='security_th_is_fals_6ae7f7_idx'),
        ),
        migrations.AddIndex(
            model_name='securitysystemmetrics',
            index=models.Index(fields=['service', 'timestamp'], name='security_se_service_6a88fb_idx'),
        ),
        migrations.AddIndex(
            model_name='securitysystemmetrics',
            index=models.Index(fields=['timestamp'], name='security_se_timesta_9d0253_idx'),
        ),
        migrations.AddIndex(
            model_name='securityreport',
            index=models.Index(fields=['report_type', 'status'], name='security_se_report__643ee9_idx'),
        ),
        migrations.AddIndex(
            model_name='securityreport',
            index=models.Index(fields=['generated_at'], name='security_se_generat_5431f3_idx'),
        ),
        migrations.AddIndex(
            model_name='securityreport',
            index=models.Index(fields=['next_generation'], name='security_se_next_ge_cd458a_idx'),
        ),
    ]
