# Prometheus configuration for TrustVault Security Monitoring
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'trustvault-security'
    environment: 'production'
    datacenter: 'primary'

rule_files:
  - "security_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node Exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # cAdvisor for container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

  # PostgreSQL metrics
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # Nginx metrics
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']

  # Django application metrics
  - job_name: 'django'
    static_configs:
      - targets: ['django:8000']
    metrics_path: '/metrics/'
    scrape_interval: 30s

  # Blackbox exporter for endpoint monitoring
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - http://nginx:80
        - http://django:8000/health/
        - http://react:80/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # Security-specific monitoring
  - job_name: 'wazuh-manager'
    static_configs:
      - targets: ['wazuh-manager:55000']
    metrics_path: '/security/events'
    scrape_interval: 60s

  # ModSecurity metrics (if available)
  - job_name: 'modsecurity'
    static_configs:
      - targets: ['modsecurity:80']
    metrics_path: '/status'
    scrape_interval: 30s

  # Fail2Ban metrics (custom exporter)
  - job_name: 'fail2ban'
    static_configs:
      - targets: ['fail2ban:9191']
    scrape_interval: 60s

  # Vault metrics
  - job_name: 'vault'
    static_configs:
      - targets: ['vault:8200']
    metrics_path: '/v1/sys/metrics'
    params:
      format: ['prometheus']
    scrape_interval: 30s

# Remote write configuration for long-term storage
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint"
#     basic_auth:
#       username: "username"
#       password: "password"


