# HashiCorp Vault Configuration for TrustVault
# Secure secrets management and encryption key storage

# Storage backend
storage "file" {
  path = "/vault/data"
}

# Listener configuration
listener "tcp" {
  address     = "0.0.0.0:8200"
  tls_disable = 1
  # TODO: Enable TLS in production with proper certificates
  # tls_cert_file   = "/vault/tls/vault.crt"
  # tls_key_file    = "/vault/tls/vault.key"
  # tls_min_version = "tls12"
}

# API address
api_addr = "http://0.0.0.0:8200"

# Cluster address
cluster_addr = "http://0.0.0.0:8201"

# UI configuration
ui = true

# Disable mlock for development
disable_mlock = true

# Log level
log_level = "Info"

# Default lease TTL
default_lease_ttl = "168h"

# Maximum lease TTL
max_lease_ttl = "720h"

# Plugin directory
plugin_directory = "/vault/plugins"

# Disable raw endpoint for security
raw_storage_endpoint = false

# Telemetry
telemetry {
  prometheus_retention_time = "30s"
  disable_hostname = true
}

# Seal configuration (for production, use auto-unseal)
# seal "awskms" {
#   region     = "us-east-1"
#   kms_key_id = "alias/vault-unseal-key"
# }

# Enterprise features (if using Vault Enterprise)
# license_path = "/vault/license/vault.hclic"

# Entropy augmentation (for production)
# entropy "seal" {
#   mode = "augmentation"
# }

# High availability (for production cluster)
# ha_storage "consul" {
#   address = "consul:8500"
#   path    = "vault/"
# }

# Audit devices
# audit "file" {
#   file_path = "/vault/logs/audit.log"
# }

# Performance settings
# cache_size = "32000"

# Cluster configuration for multi-node setup
# cluster_name = "trustvault-vault-cluster"

# Replication (Vault Enterprise)
# replication {
#   performance {
#     mode = "primary"
#   }
#   dr {
#     mode = "primary"
#   }
# }
