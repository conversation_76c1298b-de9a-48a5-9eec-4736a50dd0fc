# TrustVault - Security Views
# Comprehensive cybersecurity management interface

from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.utils import timezone
from django.db import models
from datetime import timed<PERSON>ta
from drf_spectacular.utils import extend_schema
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator
from .models import (
    SecurityEvent, ThreatIntelligence, IncidentResponse, ComplianceAudit,
    CyberSecurityService, SecuritySystemMetrics, ThreatDetection,
    SecurityDashboard, SystemIntegration, SecurityReport
)
from .cybersecurity_manager import CyberSecurityManager
from .advanced_crypto import crypto
from .intrusion_detection import ids
from .compliance import compliance_framework
# AuditLog from django-audit-log package
try:
    from audit_log.models import LogEntry as AuditLog
except ImportError:
    # Fallback if audit_log is not available
    AuditLog = None

# LoginAttempt simulation for metrics
class LoginAttemptMetrics:
    """Login attempt metrics simulation"""
    @staticmethod
    def get_metrics():
        return {
            'total_attempts': 150,
            'failed_last_24h': 5,
            'successful_last_24h': 45,
            'suspicious_last_24h': 2,
        }


@method_decorator(ratelimit(key='ip', rate='100/m', method='GET'), name='get')
class SecurityAPIRootView(APIView):
    """Vue racine de l'API de sécurité"""
    permission_classes = []  # Accès public pour la vue racine

    def get(self, request):
        """Retourne les endpoints disponibles de l'API de sécurité"""

        endpoints = {
            'dashboard': request.build_absolute_uri('dashboard/'),
            'events': request.build_absolute_uri('events/'),
            'audit_logs': request.build_absolute_uri('audit-logs/'),
            'metrics': request.build_absolute_uri('metrics/'),
            'analyze': request.build_absolute_uri('analyze/'),
            'compliance': request.build_absolute_uri('compliance/'),
            'scan': request.build_absolute_uri('scan/'),
        }

        return Response({
            'message': 'TrustVault Security API',
            'version': '1.0',
            'endpoints': endpoints,
            'documentation': {
                'swagger': request.build_absolute_uri('/api/docs/'),
                'redoc': request.build_absolute_uri('/api/redoc/'),
                'schema': request.build_absolute_uri('/api/schema/')
            }
        })


class SecurityDashboardView(APIView):
    """Security dashboard with key metrics."""

    permission_classes = []  # Temporaire : accès public pour les tests
    
    @extend_schema(
        summary="Security Dashboard",
        description="Get security metrics and recent events",
        responses={200: {"description": "Security dashboard data"}},
        tags=["Security"]
    )
    def get(self, request):
        """Get security dashboard data."""
        
        # Time ranges
        now = timezone.now()
        last_24h = now - timedelta(hours=24)
        last_7d = now - timedelta(days=7)
        last_30d = now - timedelta(days=30)
        
        # Security events metrics
        security_events = {
            'total_events': SecurityEvent.objects.count(),
            'last_24h': SecurityEvent.objects.filter(created_at__gte=last_24h).count(),
            'last_7d': SecurityEvent.objects.filter(created_at__gte=last_7d).count(),
            'unresolved': SecurityEvent.objects.filter(is_resolved=False).count(),
            'critical': SecurityEvent.objects.filter(risk_level='CRITICAL').count(),
            'high': SecurityEvent.objects.filter(risk_level='HIGH').count(),
        }
        
        # Login attempts metrics
        login_attempts = LoginAttemptMetrics.get_metrics()
        
        # Recent security events
        recent_events = SecurityEvent.objects.filter(
            created_at__gte=last_7d
        ).order_by('-created_at')[:10]

        recent_events_data = []
        for event in recent_events:
            recent_events_data.append({
                'id': str(event.id),
                'event_type': event.event_type,
                'risk_level': event.risk_level,
                'source_ip': event.source_ip,
                'description': event.description,
                'created_at': event.created_at,
                'is_resolved': event.is_resolved
            })
        
        # Top threat sources
        threat_sources = SecurityEvent.objects.filter(
            created_at__gte=last_30d
        ).values('source_ip').annotate(
            count=models.Count('id')
        ).order_by('-count')[:10]
        
        return Response({
            'security_events': security_events,
            'login_attempts': login_attempts,
            'recent_events': recent_events_data,
            'threat_sources': list(threat_sources),
            'generated_at': now
        })


class SecurityEventsListView(generics.ListAPIView):
    """List security events with filtering."""

    permission_classes = []  # Temporaire : accès public pour les tests
    
    @extend_schema(
        summary="List Security Events",
        description="Get paginated list of security events",
        responses={200: {"description": "List of security events"}},
        tags=["Security"]
    )
    def get_queryset(self):
        """Get filtered security events."""
        queryset = SecurityEvent.objects.all().order_by('-created_at')

        # Filter by risk level
        risk_level = self.request.query_params.get('risk_level')
        if risk_level:
            queryset = queryset.filter(risk_level=risk_level)

        # Filter by action (equivalent to event_type)
        action = self.request.query_params.get('action')
        if action:
            queryset = queryset.filter(action=action)

        # Filter by resolution status
        resolved = self.request.query_params.get('resolved')
        if resolved is not None:
            queryset = queryset.filter(resolved=resolved.lower() == 'true')

        # Filter by date range
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')

        if date_from:
            queryset = queryset.filter(created_at__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__lte=date_to)

        return queryset
    
    def list(self, request, *args, **kwargs):
        """List security events."""
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            data = []
            for event in page:
                data.append({
                    'id': str(event.id),
                    'event_type': event.event_type,
                    'risk_level': event.risk_level,
                    'source_ip': event.source_ip,
                    'user_agent': event.user_agent,
                    'description': event.description,
                    'details': event.details,
                    'resolved': event.is_resolved,
                    'resolved_at': event.resolved_at,
                    'timestamp': event.created_at
                })

            return self.get_paginated_response(data)
        
        # If no pagination
        data = []
        for event in queryset:
            data.append({
                'id': str(event.id),
                'event_type': event.event_type,
                'risk_level': event.risk_level,
                'source_ip': event.source_ip,
                'user_agent': event.user_agent,
                'description': event.description,
                'details': event.details,
                'resolved': event.is_resolved,
                'resolved_at': event.resolved_at,
                'timestamp': event.created_at
            })
        
        return Response(data)


class AuditLogListView(generics.ListAPIView):
    """List audit logs with filtering."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @extend_schema(
        summary="List Audit Logs",
        description="Get paginated list of audit logs",
        responses={200: {"description": "List of audit logs"}},
        tags=["Security"]
    )
    def get_queryset(self):
        """Get filtered audit logs."""
        if AuditLog is None:
            return []

        queryset = AuditLog.objects.all().order_by('-timestamp')

        # Filter by action
        action = self.request.query_params.get('action')
        if action:
            queryset = queryset.filter(action=action)

        # Filter by resource type
        resource_type = self.request.query_params.get('resource_type')
        if resource_type:
            queryset = queryset.filter(resource_type=resource_type)

        # Filter by user
        user_id = self.request.query_params.get('user_id')
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        # Filter by severity
        severity = self.request.query_params.get('severity')
        if severity:
            queryset = queryset.filter(severity=severity)

        return queryset
    
    def list(self, request, *args, **kwargs):
        """List audit logs."""
        if AuditLog is None:
            # Return mock data if AuditLog is not available
            return Response({
                'count': 1,
                'results': [{
                    'id': 1,
                    'user': {
                        'id': request.user.id if request.user.is_authenticated else None,
                        'email': request.user.email if request.user.is_authenticated else None,
                        'name': request.user.get_full_name() if request.user.is_authenticated else None
                    },
                    'action': 'API_ACCESS',
                    'resource_type': 'security_audit_logs',
                    'resource_id': None,
                    'ip_address': request.META.get('REMOTE_ADDR', '127.0.0.1'),
                    'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                    'details': 'Audit log access',
                    'severity': 'INFO',
                    'timestamp': timezone.now()
                }]
            })

        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)

        if page is not None:
            data = []
            for log in page:
                data.append({
                    'id': log.id,
                    'user': {
                        'id': log.user.id if log.user else None,
                        'email': log.user.email if log.user else None,
                        'name': log.user.get_full_name() if log.user else None
                    },
                    'action': log.action,
                    'resource_type': getattr(log, 'resource_type', 'unknown'),
                    'resource_id': getattr(log, 'resource_id', None),
                    'ip_address': getattr(log, 'ip_address', ''),
                    'user_agent': getattr(log, 'user_agent', ''),
                    'details': getattr(log, 'details', ''),
                    'severity': getattr(log, 'severity', 'INFO'),
                    'timestamp': log.timestamp
                })

            return self.get_paginated_response(data)

        return Response([])


class SecurityMetricsView(APIView):
    """Security metrics endpoint."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @extend_schema(
        summary="Security Metrics",
        description="Get detailed security metrics",
        responses={200: {"description": "Security metrics data"}},
        tags=["Security"]
    )
    def get(self, request):
        """Get security metrics."""
        from django.db import models
        
        # Time ranges
        now = timezone.now()
        last_24h = now - timedelta(hours=24)
        last_7d = now - timedelta(days=7)
        last_30d = now - timedelta(days=30)
        
        # Action distribution (equivalent to event_type)
        actions = SecurityEvent.objects.filter(
            created_at__gte=last_30d
        ).values('event_type').annotate(
            count=models.Count('id')
        ).order_by('-count')

        # Risk level distribution
        risk_levels = SecurityEvent.objects.filter(
            created_at__gte=last_30d
        ).values('risk_level').annotate(
            count=models.Count('id')
        ).order_by('-count')

        # Daily event counts for the last 30 days
        daily_events = []
        for i in range(30):
            date = (now - timedelta(days=i)).date()
            count = SecurityEvent.objects.filter(
                created_at__date=date
            ).count()
            daily_events.append({
                'date': date,
                'count': count
            })
        
        return Response({
            'actions': list(actions),
            'risk_levels': list(risk_levels),
            'daily_events': daily_events,
            'generated_at': now
        })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def analyze_request_security(request):
    """Analyse de sécurité d'une requête"""

    request_data = {
        'ip_address': request.META.get('REMOTE_ADDR', '127.0.0.1'),
        'user_agent': request.META.get('HTTP_USER_AGENT', ''),
        'method': request.method,
        'path': request.path,
        'query_params': dict(request.GET),
        'post_params': dict(request.POST) if request.method == 'POST' else {}
    }

    # Analyser la requête avec le système IDS
    analysis = ids.analyze_request(request_data)

    return Response({
        'analysis': analysis,
        'timestamp': timezone.now().isoformat()
    })


@api_view(['GET'])
@permission_classes([permissions.IsAdminUser])
def compliance_status(request):
    """Statut de conformité"""

    framework = request.GET.get('framework', 'ISO_27001')

    try:
        assessment = compliance_framework.assess_compliance(framework)
        return Response(assessment)
    except ValueError as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )


@api_view(['POST'])
@permission_classes([permissions.IsAdminUser])
def trigger_security_scan(request):
    """Déclencher un scan de sécurité"""

    scan_type = request.data.get('scan_type', 'basic')

    # Simulation d'un scan de sécurité
    scan_result = {
        'scan_id': crypto.generate_secure_token(16),
        'scan_type': scan_type,
        'status': 'initiated',
        'started_at': timezone.now().isoformat(),
        'estimated_duration': '5-10 minutes'
    }

    return Response(scan_result, status=status.HTTP_202_ACCEPTED)


# ============================================================================
# CYBERSECURITY SYSTEMS MANAGEMENT VIEWS
# ============================================================================

class CyberSecurityOverviewView(APIView):
    """Comprehensive cybersecurity systems overview."""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Cybersecurity Systems Overview",
        description="Get comprehensive overview of all cybersecurity systems",
        responses={200: {"description": "Cybersecurity systems overview"}},
        tags=["Cybersecurity Management"]
    )
    def get(self, request):
        """Get cybersecurity systems overview."""
        manager = CyberSecurityManager()

        # Get all services
        services = CyberSecurityService.objects.all()
        services_data = []

        for service in services:
            services_data.append({
                'id': service.id,
                'name': service.name,
                'service_type': service.service_type,
                'status': service.status,
                'health_status': service.health_status,
                'uptime_percentage': float(service.uptime_percentage),
                'response_time_ms': service.response_time_ms,
                'last_health_check': service.last_health_check,
                'endpoint_url': service.endpoint_url,
                'is_monitoring_enabled': service.is_monitoring_enabled
            })

        # Get recent threats
        recent_threats = ThreatDetection.objects.select_related('detecting_service').order_by('-created_at')[:10]
        threats_data = []

        for threat in recent_threats:
            threats_data.append({
                'id': threat.id,
                'threat_category': threat.threat_category,
                'severity': threat.severity,
                'confidence_score': float(threat.confidence_score),
                'source_ip': threat.source_ip,
                'target_ip': threat.target_ip,
                'action_taken': threat.action_taken,
                'detecting_service': threat.detecting_service.name,
                'created_at': threat.created_at,
                'is_false_positive': threat.is_false_positive
            })

        # Get system statistics
        now = timezone.now()
        last_24h = now - timedelta(hours=24)

        stats = {
            'total_services': services.count(),
            'active_services': services.filter(status='ACTIVE').count(),
            'healthy_services': services.filter(health_status='HEALTHY').count(),
            'threats_detected_24h': ThreatDetection.objects.filter(created_at__gte=last_24h).count(),
            'threats_blocked_24h': ThreatDetection.objects.filter(
                created_at__gte=last_24h, action_taken='BLOCKED'
            ).count(),
            'critical_threats_24h': ThreatDetection.objects.filter(
                created_at__gte=last_24h, severity='CRITICAL'
            ).count(),
        }

        return Response({
            'services': services_data,
            'recent_threats': threats_data,
            'statistics': stats,
            'generated_at': now
        })


class CyberSecurityServicesView(APIView):
    """Manage cybersecurity services."""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="List Cybersecurity Services",
        description="Get list of all cybersecurity services",
        responses={200: {"description": "List of cybersecurity services"}},
        tags=["Cybersecurity Management"]
    )
    def get(self, request):
        """List all cybersecurity services."""
        services = CyberSecurityService.objects.all()
        data = []

        for service in services:
            # Get latest metrics
            latest_metrics = SecuritySystemMetrics.objects.filter(
                service=service
            ).order_by('-timestamp').first()

            service_data = {
                'id': service.id,
                'name': service.name,
                'service_type': service.service_type,
                'description': service.description,
                'status': service.status,
                'health_status': service.health_status,
                'endpoint_url': service.endpoint_url,
                'uptime_percentage': float(service.uptime_percentage),
                'response_time_ms': service.response_time_ms,
                'last_health_check': service.last_health_check,
                'is_monitoring_enabled': service.is_monitoring_enabled,
                'created_at': service.created_at,
                'updated_at': service.updated_at
            }

            if latest_metrics:
                service_data['latest_metrics'] = {
                    'events_processed': latest_metrics.events_processed,
                    'threats_detected': latest_metrics.threats_detected,
                    'threats_blocked': latest_metrics.threats_blocked,
                    'error_rate': float(latest_metrics.error_rate),
                    'timestamp': latest_metrics.timestamp
                }

            data.append(service_data)

        return Response(data)

    @extend_schema(
        summary="Create Cybersecurity Service",
        description="Create a new cybersecurity service",
        responses={201: {"description": "Service created successfully"}},
        tags=["Cybersecurity Management"]
    )
    def post(self, request):
        """Create a new cybersecurity service."""
        data = request.data

        service = CyberSecurityService.objects.create(
            name=data.get('name'),
            service_type=data.get('service_type'),
            description=data.get('description', ''),
            endpoint_url=data.get('endpoint_url', ''),
            config_data=data.get('config_data', {}),
            is_monitoring_enabled=data.get('is_monitoring_enabled', True)
        )

        return Response({
            'id': service.id,
            'name': service.name,
            'service_type': service.service_type,
            'status': service.status,
            'message': 'Service created successfully'
        }, status=status.HTTP_201_CREATED)


class CyberSecurityServiceDetailView(APIView):
    """Manage individual cybersecurity service."""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Get Cybersecurity Service Details",
        description="Get detailed information about a specific cybersecurity service",
        responses={200: {"description": "Service details"}},
        tags=["Cybersecurity Management"]
    )
    def get(self, request, service_id):
        """Get service details."""
        try:
            service = CyberSecurityService.objects.get(id=service_id)
        except CyberSecurityService.DoesNotExist:
            return Response(
                {'error': 'Service not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get recent metrics
        recent_metrics = SecuritySystemMetrics.objects.filter(
            service=service
        ).order_by('-timestamp')[:24]  # Last 24 data points

        metrics_data = []
        for metric in recent_metrics:
            metrics_data.append({
                'timestamp': metric.timestamp,
                'events_processed': metric.events_processed,
                'threats_detected': metric.threats_detected,
                'threats_blocked': metric.threats_blocked,
                'response_time': float(metric.response_time) if metric.response_time else None,
                'error_rate': float(metric.error_rate),
                'cpu_usage': float(metric.cpu_usage) if metric.cpu_usage else None,
                'memory_usage': float(metric.memory_usage) if metric.memory_usage else None
            })

        # Get recent threats detected by this service
        recent_threats = ThreatDetection.objects.filter(
            detecting_service=service
        ).order_by('-created_at')[:10]

        threats_data = []
        for threat in recent_threats:
            threats_data.append({
                'id': threat.id,
                'threat_category': threat.threat_category,
                'severity': threat.severity,
                'confidence_score': float(threat.confidence_score),
                'source_ip': threat.source_ip,
                'action_taken': threat.action_taken,
                'created_at': threat.created_at
            })

        return Response({
            'service': {
                'id': service.id,
                'name': service.name,
                'service_type': service.service_type,
                'description': service.description,
                'status': service.status,
                'health_status': service.health_status,
                'endpoint_url': service.endpoint_url,
                'uptime_percentage': float(service.uptime_percentage),
                'response_time_ms': service.response_time_ms,
                'last_health_check': service.last_health_check,
                'is_monitoring_enabled': service.is_monitoring_enabled,
                'config_data': service.config_data,
                'created_at': service.created_at,
                'updated_at': service.updated_at
            },
            'recent_metrics': metrics_data,
            'recent_threats': threats_data
        })

    @extend_schema(
        summary="Update Cybersecurity Service",
        description="Update cybersecurity service configuration",
        responses={200: {"description": "Service updated successfully"}},
        tags=["Cybersecurity Management"]
    )
    def put(self, request, service_id):
        """Update service configuration."""
        try:
            service = CyberSecurityService.objects.get(id=service_id)
        except CyberSecurityService.DoesNotExist:
            return Response(
                {'error': 'Service not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        data = request.data

        # Update service fields
        if 'name' in data:
            service.name = data['name']
        if 'description' in data:
            service.description = data['description']
        if 'endpoint_url' in data:
            service.endpoint_url = data['endpoint_url']
        if 'config_data' in data:
            service.config_data = data['config_data']
        if 'is_monitoring_enabled' in data:
            service.is_monitoring_enabled = data['is_monitoring_enabled']
        if 'status' in data:
            service.status = data['status']

        service.save()

        return Response({
            'id': service.id,
            'name': service.name,
            'status': service.status,
            'message': 'Service updated successfully'
        })

    @extend_schema(
        summary="Delete Cybersecurity Service",
        description="Delete a cybersecurity service",
        responses={204: {"description": "Service deleted successfully"}},
        tags=["Cybersecurity Management"]
    )
    def delete(self, request, service_id):
        """Delete service."""
        try:
            service = CyberSecurityService.objects.get(id=service_id)
            service.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except CyberSecurityService.DoesNotExist:
            return Response(
                {'error': 'Service not found'},
                status=status.HTTP_404_NOT_FOUND
            )


class SystemHealthCheckView(APIView):
    """Perform health checks on all cybersecurity systems."""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="System Health Check",
        description="Perform health check on all cybersecurity systems",
        responses={200: {"description": "Health check results"}},
        tags=["Cybersecurity Management"]
    )
    def post(self, request):
        """Perform health check on all systems."""
        manager = CyberSecurityManager()
        health_results = manager.check_all_services_health()

        return Response({
            'health_check_results': health_results,
            'timestamp': timezone.now(),
            'total_services': len(health_results),
            'healthy_services': len([r for r in health_results.values() if r.get('status') == 'HEALTHY']),
            'critical_services': len([r for r in health_results.values() if r.get('status') == 'CRITICAL'])
        })


class ThreatDetectionView(APIView):
    """Manage threat detection results."""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="List Threat Detections",
        description="Get list of threat detections with filtering",
        responses={200: {"description": "List of threat detections"}},
        tags=["Cybersecurity Management"]
    )
    def get(self, request):
        """List threat detections."""
        queryset = ThreatDetection.objects.select_related('detecting_service').order_by('-created_at')

        # Apply filters
        service_id = request.query_params.get('service_id')
        if service_id:
            queryset = queryset.filter(detecting_service_id=service_id)

        threat_category = request.query_params.get('threat_category')
        if threat_category:
            queryset = queryset.filter(threat_category=threat_category)

        severity = request.query_params.get('severity')
        if severity:
            queryset = queryset.filter(severity=severity)

        action_taken = request.query_params.get('action_taken')
        if action_taken:
            queryset = queryset.filter(action_taken=action_taken)

        is_false_positive = request.query_params.get('is_false_positive')
        if is_false_positive is not None:
            queryset = queryset.filter(is_false_positive=is_false_positive.lower() == 'true')

        # Date range filtering
        date_from = request.query_params.get('date_from')
        date_to = request.query_params.get('date_to')
        if date_from:
            queryset = queryset.filter(created_at__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__lte=date_to)

        # Pagination
        page = self.paginate_queryset(queryset) if hasattr(self, 'paginate_queryset') else queryset[:50]

        data = []
        for threat in (page if page else queryset):
            data.append({
                'id': threat.id,
                'threat_category': threat.threat_category,
                'severity': threat.severity,
                'confidence_score': float(threat.confidence_score),
                'source_ip': threat.source_ip,
                'source_port': threat.source_port,
                'target_ip': threat.target_ip,
                'target_port': threat.target_port,
                'attack_signature': threat.attack_signature,
                'action_taken': threat.action_taken,
                'is_false_positive': threat.is_false_positive,
                'detecting_service': {
                    'id': threat.detecting_service.id,
                    'name': threat.detecting_service.name,
                    'service_type': threat.detecting_service.service_type
                },
                'geolocation': threat.geolocation,
                'created_at': threat.created_at
            })

        return Response(data)

    @extend_schema(
        summary="Create Threat Detection",
        description="Create a new threat detection record",
        responses={201: {"description": "Threat detection created"}},
        tags=["Cybersecurity Management"]
    )
    def post(self, request):
        """Create threat detection record."""
        data = request.data

        try:
            service = CyberSecurityService.objects.get(id=data.get('detecting_service_id'))
        except CyberSecurityService.DoesNotExist:
            return Response(
                {'error': 'Detecting service not found'},
                status=status.HTTP_400_BAD_REQUEST
            )

        threat = ThreatDetection.objects.create(
            detecting_service=service,
            threat_category=data.get('threat_category'),
            severity=data.get('severity'),
            confidence_score=data.get('confidence_score', 0),
            source_ip=data.get('source_ip'),
            source_port=data.get('source_port'),
            target_ip=data.get('target_ip'),
            target_port=data.get('target_port'),
            attack_signature=data.get('attack_signature', ''),
            payload=data.get('payload', ''),
            user_agent=data.get('user_agent', ''),
            action_taken=data.get('action_taken'),
            geolocation=data.get('geolocation', {}),
            threat_intelligence=data.get('threat_intelligence', {}),
            raw_data=data.get('raw_data', {})
        )

        return Response({
            'id': threat.id,
            'threat_category': threat.threat_category,
            'severity': threat.severity,
            'message': 'Threat detection created successfully'
        }, status=status.HTTP_201_CREATED)


# Security Testing Endpoints for Penetration Testing
class SecurityTestEndpointView(APIView):
    """
    Security test endpoint for SQL injection testing.
    This endpoint is designed to be tested by security scanners.
    """
    permission_classes = []  # No authentication required for testing

    def get(self, request):
        """Handle GET requests with potential SQL injection payloads"""
        # The security middleware will catch malicious patterns
        # and return 403 before reaching this code
        return Response({
            'message': 'Test endpoint active',
            'timestamp': timezone.now(),
            'parameters': dict(request.GET)
        })


class SecuritySearchTestView(APIView):
    """
    Security search test endpoint for XSS testing.
    This endpoint is designed to be tested by security scanners.
    """
    permission_classes = []  # No authentication required for testing

    def get(self, request):
        """Handle GET requests with potential XSS payloads"""
        # The security middleware will catch malicious patterns
        # and return 403 before reaching this code
        query = request.GET.get('q', '')
        return Response({
            'message': 'Search test endpoint active',
            'query': query,
            'timestamp': timezone.now()
        })


class SecurityPingTestView(APIView):
    """
    Security ping test endpoint for command injection testing.
    This endpoint is designed to be tested by security scanners.
    """
    permission_classes = []  # No authentication required for testing

    def get(self, request):
        """Handle GET requests with potential command injection payloads"""
        # The security middleware will catch malicious patterns
        # and return 403 before reaching this code
        host = request.GET.get('host', 'localhost')
        return Response({
            'message': 'Ping test endpoint active',
            'host': host,
            'timestamp': timezone.now()
        })
