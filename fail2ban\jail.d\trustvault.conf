[trustvault-auth]
enabled = true
port = http,https
filter = trustvault-auth
logpath = /var/log/nginx/access.log
          /var/log/trustvault/django.log
maxretry = 5
findtime = 300
bantime = 3600
action = iptables-multiport[name=trustvault-auth, port="http,https", protocol=tcp]

[nginx-limit-req]
enabled = true
port = http,https
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
maxretry = 10
findtime = 600
bantime = 7200
action = iptables-multiport[name=nginx-limit-req, port="http,https", protocol=tcp]
