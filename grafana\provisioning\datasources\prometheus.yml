apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "5s"
      queryTimeout: "60s"
      httpMethod: "POST"
    secureJsonData: {}

  - name: TrustVault-Logs
    type: loki
    access: proxy
    url: http://loki:3100
    isDefault: false
    editable: true
    jsonData:
      maxLines: 1000
    secureJsonData: {}

  - name: TrustVault-Postgres
    type: postgres
    access: proxy
    url: postgres:5432
    database: trustvault
    user: trustvault
    isDefault: false
    editable: true
    jsonData:
      sslmode: "disable"
      maxOpenConns: 0
      maxIdleConns: 2
      connMaxLifetime: 14400
    secureJsonData:
      password: "SecureDBPassword123!TrustVault2024"
