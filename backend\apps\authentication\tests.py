# TrustVault - Authentication Tests

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from django_otp.plugins.otp_totp.models import TOTPDevice

from .models import UserSession
from apps.core.models import AuditLog

User = get_user_model()


class UserModelTest(TestCase):
    """Test User model functionality."""
    
    def setUp(self):
        self.user_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpass123'
        }
    
    def test_create_user(self):
        """Test creating a regular user."""
        user = User.objects.create_user(**self.user_data)
        
        self.assertEqual(user.email, self.user_data['email'])
        self.assertEqual(user.username, self.user_data['username'])
        self.assertTrue(user.check_password(self.user_data['password']))
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)
    
    def test_create_superuser(self):
        """Test creating a superuser."""
        user = User.objects.create_superuser(**self.user_data)
        
        self.assertEqual(user.email, self.user_data['email'])
        self.assertTrue(user.is_active)
        self.assertTrue(user.is_staff)
        self.assertTrue(user.is_superuser)
    
    def test_user_string_representation(self):
        """Test user string representation."""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(str(user), self.user_data['email'])


class AuthenticationAPITest(APITestCase):
    """Test authentication API endpoints."""
    
    def setUp(self):
        self.register_url = reverse('authentication:register')
        self.login_url = reverse('authentication:login')
        self.logout_url = reverse('authentication:logout')
        self.profile_url = reverse('authentication:profile')
        
        self.user_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpass123',
            'password_confirm': 'testpass123'
        }
    
    def test_user_registration(self):
        """Test user registration endpoint."""
        response = self.client.post(self.register_url, self.user_data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('message', response.data)
        self.assertIn('user_id', response.data)
        
        # Verify user was created
        user = User.objects.get(email=self.user_data['email'])
        self.assertEqual(user.username, self.user_data['username'])
    
    def test_user_registration_invalid_data(self):
        """Test user registration with invalid data."""
        invalid_data = self.user_data.copy()
        invalid_data['password_confirm'] = 'different_password'
        
        response = self.client.post(self.register_url, invalid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_user_login(self):
        """Test user login endpoint."""
        # Create user first
        user = User.objects.create_user(
            email=self.user_data['email'],
            username=self.user_data['username'],
            password=self.user_data['password']
        )
        
        login_data = {
            'email': self.user_data['email'],
            'password': self.user_data['password']
        }
        
        response = self.client.post(self.login_url, login_data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)
    
    def test_user_login_invalid_credentials(self):
        """Test user login with invalid credentials."""
        login_data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        
        response = self.client.post(self.login_url, login_data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_user_profile_authenticated(self):
        """Test accessing user profile when authenticated."""
        user = User.objects.create_user(
            email=self.user_data['email'],
            username=self.user_data['username'],
            password=self.user_data['password']
        )
        
        # Authenticate user
        refresh = RefreshToken.for_user(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        response = self.client.get(self.profile_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['email'], user.email)
        self.assertEqual(response.data['username'], user.username)
    
    def test_user_profile_unauthenticated(self):
        """Test accessing user profile when not authenticated."""
        response = self.client.get(self.profile_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class MFATest(APITestCase):
    """Test MFA functionality."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
        
        # Authenticate user
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        self.mfa_setup_url = reverse('authentication:mfa-setup')
        self.mfa_verify_url = reverse('authentication:mfa-verify')
        self.mfa_disable_url = reverse('authentication:mfa-disable')
    
    def test_mfa_setup(self):
        """Test MFA setup endpoint."""
        response = self.client.post(self.mfa_setup_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('qr_code_url', response.data)
        self.assertIn('secret_key', response.data)
        
        # Verify TOTP device was created
        device = TOTPDevice.objects.filter(user=self.user).first()
        self.assertIsNotNone(device)
        self.assertFalse(device.confirmed)
    
    def test_mfa_setup_already_enabled(self):
        """Test MFA setup when already enabled."""
        # Enable MFA first
        self.user.is_mfa_enabled = True
        self.user.save()
        
        response = self.client.post(self.mfa_setup_url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_mfa_disable(self):
        """Test MFA disable endpoint."""
        # Setup MFA first
        self.user.is_mfa_enabled = True
        self.user.save()
        
        TOTPDevice.objects.create(
            user=self.user,
            name='default',
            confirmed=True
        )
        
        response = self.client.post(self.mfa_disable_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify MFA was disabled
        self.user.refresh_from_db()
        self.assertFalse(self.user.is_mfa_enabled)
        
        # Verify TOTP devices were removed
        device_count = TOTPDevice.objects.filter(user=self.user).count()
        self.assertEqual(device_count, 0)


class UserSessionTest(TestCase):
    """Test UserSession model functionality."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
    
    def test_create_user_session(self):
        """Test creating a user session."""
        session = UserSession.objects.create(
            user=self.user,
            session_key='test_session_key',
            ip_address='127.0.0.1',
            user_agent='Test User Agent'
        )
        
        self.assertEqual(session.user, self.user)
        self.assertEqual(session.session_key, 'test_session_key')
        self.assertEqual(session.ip_address, '127.0.0.1')
        self.assertTrue(session.is_active)
    
    def test_user_session_string_representation(self):
        """Test user session string representation."""
        session = UserSession.objects.create(
            user=self.user,
            session_key='test_session_key',
            ip_address='127.0.0.1',
            user_agent='Test User Agent'
        )
        
        expected_str = f"{self.user.email} - 127.0.0.1"
        self.assertEqual(str(session), expected_str)


class AuditLogTest(TestCase):
    """Test AuditLog functionality."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
    
    def test_create_audit_log(self):
        """Test creating an audit log entry."""
        log = AuditLog.objects.create(
            user=self.user,
            action='TEST_ACTION',
            resource_type='TestResource',
            resource_id='123',
            ip_address='127.0.0.1',
            user_agent='Test User Agent',
            details={'test': 'data'}
        )
        
        self.assertEqual(log.user, self.user)
        self.assertEqual(log.action, 'TEST_ACTION')
        self.assertEqual(log.resource_type, 'TestResource')
        self.assertEqual(log.resource_id, '123')
        self.assertEqual(log.details, {'test': 'data'})
    
    def test_audit_log_string_representation(self):
        """Test audit log string representation."""
        log = AuditLog.objects.create(
            user=self.user,
            action='TEST_ACTION',
            resource_type='TestResource',
            resource_id='123',
            ip_address='127.0.0.1',
            user_agent='Test User Agent'
        )
        
        expected_str = f"{self.user.email} - TEST_ACTION - TestResource:123"
        self.assertEqual(str(log), expected_str)
