# TrustVault - Security URLs

from django.urls import path
from . import views

app_name = 'security'

urlpatterns = [
    # Original security endpoints
    path('', views.SecurityAPIRootView.as_view(), name='security-root'),
    path('dashboard/', views.SecurityDashboardView.as_view(), name='dashboard'),
    path('events/', views.SecurityEventsListView.as_view(), name='events'),
    path('audit-logs/', views.AuditLogListView.as_view(), name='audit-logs'),
    path('metrics/', views.SecurityMetricsView.as_view(), name='metrics'),
    path('analyze/', views.analyze_request_security, name='analyze-request'),
    path('compliance/', views.compliance_status, name='compliance-status'),
    path('scan/', views.trigger_security_scan, name='trigger-scan'),

    # Cybersecurity Systems Management
    path('cybersecurity/', views.CyberSecurityOverviewView.as_view(), name='cybersecurity-overview'),
    path('cybersecurity/services/', views.CyberSecurityServicesView.as_view(), name='cybersecurity-services'),
    path('cybersecurity/services/<int:service_id>/', views.CyberSecurityServiceDetailView.as_view(), name='cybersecurity-service-detail'),
    path('cybersecurity/health-check/', views.SystemHealthCheckView.as_view(), name='system-health-check'),
    path('cybersecurity/threats/', views.ThreatDetectionView.as_view(), name='threat-detections'),

    # Security Testing Endpoints (for penetration testing)
    path('test/', views.SecurityTestEndpointView.as_view(), name='security-test'),
    path('search/', views.SecuritySearchTestView.as_view(), name='security-search-test'),
    path('ping/', views.SecurityPingTestView.as_view(), name='security-ping-test'),
]
