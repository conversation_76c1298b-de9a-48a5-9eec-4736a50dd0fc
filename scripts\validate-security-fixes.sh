#!/bin/bash
# Security Validation Script

echo "🔍 Validating Security Enhancements..."
echo "====================================="

# Test WAF functionality
echo "Testing WAF Protection..."
curl -s -o /dev/null -w "%{http_code}" "http://localhost/api/test?id=1' OR '1'='1" | grep -q "403" && echo "✅ SQL Injection blocked" || echo "❌ SQL Injection not blocked"
curl -s -o /dev/null -w "%{http_code}" "http://localhost/api/test?search=<script>alert('xss')</script>" | grep -q "403" && echo "✅ XSS blocked" || echo "❌ XSS not blocked"
curl -s -o /dev/null -w "%{http_code}" "http://localhost/../../../etc/passwd" | grep -q "403" && echo "✅ Path traversal blocked" || echo "❌ Path traversal not blocked"

# Test security headers
echo "Testing Security Headers..."
HEADERS=$(curl -s -I https://localhost 2>/dev/null || curl -s -I http://localhost)
echo "$HEADERS" | grep -q "Strict-Transport-Security" && echo "✅ HSTS header present" || echo "❌ HSTS header missing"
echo "$HEADERS" | grep -q "X-Content-Type-Options" && echo "✅ Content-Type-Options header present" || echo "❌ Content-Type-Options header missing"
echo "$HEADERS" | grep -q "X-Frame-Options" && echo "✅ X-Frame-Options header present" || echo "❌ X-Frame-Options header missing"
echo "$HEADERS" | grep -q "Content-Security-Policy" && echo "✅ CSP header present" || echo "❌ CSP header missing"

# Test rate limiting
echo "Testing Rate Limiting..."
for i in {1..15}; do
    curl -s -o /dev/null "http://localhost/api/test" &
done
wait
sleep 1
curl -s -o /dev/null -w "%{http_code}" "http://localhost/api/test" | grep -q "429\|503" && echo "✅ Rate limiting active" || echo "❌ Rate limiting not working"

echo "Security validation completed!"
