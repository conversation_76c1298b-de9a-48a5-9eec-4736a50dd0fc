-- TrustVault - Database Initialization
-- This script ensures the trustvault database exists

-- Create the database if it doesn't exist
SELECT 'CREATE DATABASE trustvault'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'trustvault')\gexec

-- Connect to the trustvault database
\c trustvault

-- Grant necessary permissions to the trustvault user
GRANT ALL PRIVILEGES ON DATABASE trustvault TO trustvault;
GRANT ALL PRIVILEGES ON SCHEMA public TO trustvault;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO trustvault;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO trustvault;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO trustvault;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO trustvault;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO trustvault;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO trustvault;
