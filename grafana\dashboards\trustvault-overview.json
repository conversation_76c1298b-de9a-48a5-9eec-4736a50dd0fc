{"dashboard": {"id": null, "title": "TrustVault - Security Overview", "tags": ["trustvault", "security", "overview"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "System Status", "type": "stat", "targets": [{"expr": "up{job=\"trustvault-django\"}", "legendFormat": "<PERSON><PERSON><PERSON>"}, {"expr": "up{job=\"trustvault-nginx\"}", "legendFormat": "Nginx Proxy"}, {"expr": "up{job=\"postgres\"}", "legendFormat": "Database"}, {"expr": "up{job=\"redis\"}", "legendFormat": "<PERSON><PERSON>"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Security Events", "type": "graph", "targets": [{"expr": "rate(django_http_requests_total[5m])", "legendFormat": "HTTP Requests/sec"}, {"expr": "rate(nginx_http_requests_total[5m])", "legendFormat": "Nginx Requests/sec"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Database Performance", "type": "graph", "targets": [{"expr": "pg_stat_database_tup_fetched{datname=\"trustvault\"}", "legendFormat": "Rows Fetched"}, {"expr": "pg_stat_database_tup_inserted{datname=\"trustvault\"}", "legendFormat": "Rows Inserted"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Security Alerts", "type": "table", "targets": [{"expr": "increase(alertmanager_alerts_total[1h])", "legendFormat": "<PERSON><PERSON><PERSON> Last Hour"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Container Resource Usage", "type": "graph", "targets": [{"expr": "rate(container_cpu_usage_seconds_total{name=~\"trustvault.*\"}[5m]) * 100", "legendFormat": "CPU % - {{name}}"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}}, {"id": 6, "title": "Memory Usage", "type": "graph", "targets": [{"expr": "container_memory_usage_bytes{name=~\"trustvault.*\"} / 1024 / 1024", "legendFormat": "Memory MB - {{name}}"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s"}, "overwrite": true}