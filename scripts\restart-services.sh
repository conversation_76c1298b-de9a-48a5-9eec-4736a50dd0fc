#!/bin/bash

# TrustVault - Service Restart Script
# This script properly restarts all services to fix the current issues

echo "🛡️ TrustVault - Restarting Services to Fix Issues"
echo "=================================================="

# Stop all services
echo "⏹️ Stopping all services..."
docker-compose down

# Remove problematic containers
echo "🗑️ Removing containers to ensure clean restart..."
docker-compose rm -f django celery-worker celery-beat postgres

# Clean up any orphaned containers
echo "🧹 Cleaning up orphaned containers..."
docker system prune -f

# Rebuild containers with no cache
echo "🔨 Rebuilding containers..."
docker-compose build --no-cache django

# Start database first
echo "🗄️ Starting database..."
docker-compose up -d postgres redis

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 30

# Start core services
echo "🚀 Starting core services..."
docker-compose up -d django

# Wait for Django to be ready
echo "⏳ Waiting for Django to be ready..."
sleep 20

# Start Celery services
echo "🔄 Starting Celery services..."
docker-compose up -d celery-worker celery-beat

# Start remaining services
echo "🌐 Starting remaining services..."
docker-compose up -d

echo "✅ All services restarted successfully!"
echo "📊 Checking service status..."
docker-compose ps

echo ""
echo "🔍 To monitor logs, use:"
echo "  docker-compose logs -f django"
echo "  docker-compose logs -f celery-worker"
echo "  docker-compose logs -f postgres"
