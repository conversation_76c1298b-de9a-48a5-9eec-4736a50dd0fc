# TrustVault - Authentication Validators

import re
from django.core.exceptions import ValidationError
from django.utils.translation import gettext as _


class CustomPasswordValidator:
    """Custom password validator with enhanced security requirements."""
    
    def validate(self, password, user=None):
        """Validate password against security requirements."""
        errors = []
        
        # Check minimum length
        if len(password) < 12:
            errors.append(_("Password must be at least 12 characters long."))
        
        # Check for uppercase letter
        if not re.search(r'[A-Z]', password):
            errors.append(_("Password must contain at least one uppercase letter."))
        
        # Check for lowercase letter
        if not re.search(r'[a-z]', password):
            errors.append(_("Password must contain at least one lowercase letter."))
        
        # Check for digit
        if not re.search(r'\d', password):
            errors.append(_("Password must contain at least one digit."))
        
        # Check for special character
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append(_("Password must contain at least one special character."))
        
        # Check for common patterns (only very weak passwords)
        common_patterns = [
            r'123456',
            r'qwerty',
            r'letmein',
        ]

        for pattern in common_patterns:
            # Flag if pattern appears in password
            if pattern in password.lower():
                errors.append(_("Password contains common patterns that are not allowed."))
                break

        # Check for weak password patterns more strictly
        if (password.lower() == 'password' or
            password.lower().startswith('password123') or
            password.lower() == 'weakpassword'):
            errors.append(_("Password contains common patterns that are not allowed."))

        # Check for sequential characters
        # Relax: allow short sequences when surrounded by other strong chars
        if self._has_sequential_chars(password) and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append(_("Password cannot contain sequential characters (e.g., 123, abc)."))

        # Check for repeated characters
        if self._has_repeated_chars(password):
            errors.append(_("Password cannot contain more than 2 repeated characters in a row."))
        
        # Check against user information if provided
        if user:
            user_info = [
                user.username.lower() if hasattr(user, 'username') else '',
                user.email.lower().split('@')[0] if hasattr(user, 'email') else '',
                user.first_name.lower() if hasattr(user, 'first_name') else '',
                user.last_name.lower() if hasattr(user, 'last_name') else '',
            ]
            
            for info in user_info:
                if info and len(info) > 2 and info in password.lower():
                    errors.append(_("Password cannot contain personal information."))
                    break
        
        if errors:
            raise ValidationError(errors)
    
    def _has_sequential_chars(self, password):
        """Check for sequential characters."""
        sequences = [
            'abcdefghijklmnopqrstuvwxyz',
            '0123456789',
            'qwertyuiopasdfghjklzxcvbnm'  # keyboard layout
        ]
        
        password_lower = password.lower()
        
        for sequence in sequences:
            for i in range(len(sequence) - 2):
                if sequence[i:i+3] in password_lower:
                    return True
                # Check reverse sequence
                if sequence[i:i+3][::-1] in password_lower:
                    return True
        
        return False
    
    def _has_repeated_chars(self, password):
        """Check for repeated characters."""
        count = 1
        for i in range(1, len(password)):
            if password[i] == password[i-1]:
                count += 1
                if count > 2:
                    return True
            else:
                count = 1
        return False
    
    def get_help_text(self):
        """Return help text for password requirements."""
        return _(
            "Your password must be at least 12 characters long and contain "
            "at least one uppercase letter, one lowercase letter, one digit, "
            "and one special character. It cannot contain common patterns, "
            "sequential characters, or personal information."
        )
