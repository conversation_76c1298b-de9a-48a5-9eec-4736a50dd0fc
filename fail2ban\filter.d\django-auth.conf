# Django Authentication Filter for Fail2ban
# Detects Django authentication failures

[Definition]

# Detect Django authentication failures
failregex = ^.*Invalid password for.*from <HOST>.*$
            ^.*Authentication failed.*from <HOST>.*$
            ^.*<PERSON><PERSON> failed.*from <HOST>.*$
            ^.*Invalid credentials.*from <HOST>.*$
            ^.*\[django\.contrib\.auth\].*Authentication failed.*<HOST>.*$

# Ignore successful authentications
ignoreregex = ^.*Login successful.*from <HOST>.*$
              ^.*Authentication successful.*from <HOST>.*$

# Date pattern for Django logs
datepattern = ^%%Y-%%m-%%d %%H:%%M:%%S
