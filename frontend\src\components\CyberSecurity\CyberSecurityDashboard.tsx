import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Button,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  Alert,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Badge,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Security,
  Shield,
  Warning,
  CheckCircle,
  Error,
  Refresh,
  Visibility,
  Settings,
  TrendingUp,
  Block,
  BugReport,
  NetworkCheck,
  MonitorHeart,
  Notifications,
  OpenInNew
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import api from '../../services/api';

interface CyberSecurityService {
  id: number;
  name: string;
  service_type: string;
  status: string;
  health_status: string;
  uptime_percentage: number;
  response_time_ms: number;
  last_health_check: string;
  endpoint_url: string;
  is_monitoring_enabled: boolean;
  config_data?: {
    public_url?: string;
    [key: string]: any;
  };
}

interface ThreatDetection {
  id: number;
  threat_category: string;
  severity: string;
  confidence_score: number;
  source_ip: string;
  target_ip: string;
  action_taken: string;
  detecting_service: string;
  created_at: string;
  is_false_positive: boolean;
}

interface SystemStatistics {
  total_services: number;
  active_services: number;
  healthy_services: number;
  threats_detected_24h: number;
  threats_blocked_24h: number;
  critical_threats_24h: number;
}

const CyberSecurityDashboard: React.FC = () => {
  const theme = useTheme();
  const [services, setServices] = useState<CyberSecurityService[]>([]);
  const [threats, setThreats] = useState<ThreatDetection[]>([]);
  const [statistics, setStatistics] = useState<SystemStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [healthCheckLoading, setHealthCheckLoading] = useState(false);
  const [selectedService, setSelectedService] = useState<CyberSecurityService | null>(null);
  const [serviceDetailOpen, setServiceDetailOpen] = useState(false);
  const [threatFilter, setThreatFilter] = useState<string>('all');
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [alertsEnabled, setAlertsEnabled] = useState<boolean>(true);
  const [lastThreatCount, setLastThreatCount] = useState<number>(0);

  useEffect(() => {
    loadDashboardData();
    // Auto-refresh every 15 seconds for more real-time feel
    const interval = setInterval(loadDashboardData, 15000);

    // Request notification permission
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }

    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      setError(null);
      const response = await api.get('/security/cybersecurity/');
      const newThreats = response.data.recent_threats || [];

      // Check for new critical threats and play alert sound
      if (alertsEnabled && newThreats.length > lastThreatCount) {
        const criticalThreats = newThreats.filter((t: any) => t.severity === 'HIGH' || t.severity === 'CRITICAL');
        if (criticalThreats.length > 0) {
          // Play alert sound (browser notification sound)
          try {
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            audio.play().catch(() => {
              // Fallback: show browser notification
              if (Notification.permission === 'granted') {
                new Notification('🚨 Critical Security Threat Detected!', {
                  body: `${criticalThreats.length} critical threat(s) detected`,
                  icon: '/favicon.ico'
                });
              }
            });
          } catch (e) {
            console.log('Audio alert failed:', e);
          }
        }
      }

      setServices(response.data.services || []);
      setThreats(newThreats);
      setStatistics(response.data.statistics || null);
      setLastUpdate(new Date());
      setLastThreatCount(newThreats.length);
    } catch (error: any) {
      console.error('Failed to load cybersecurity dashboard:', error);

      // Handle different types of errors
      if (error.response?.status === 401) {
        setError('Authentication failed. Please log in again.');
        console.error('Authentication failed - redirecting to login');
        // The API service will handle token refresh automatically
        // If that fails, it will redirect to login
      } else if (error.response?.status === 403) {
        setError('Access forbidden. You do not have permission to view cybersecurity data.');
        console.error('Access forbidden - insufficient permissions');
      } else if (error.response?.status >= 500) {
        setError('Server error. Please try again later.');
        console.error('Server error:', error.message);
      } else if (error.code === 'NETWORK_ERROR' || !error.response) {
        setError('Network error. Please check your connection and try again.');
        console.error('Network error:', error.message);
      } else {
        setError('Failed to load cybersecurity data. Please try again.');
        console.error('Unknown error:', error.message);
      }

      // Set empty data to prevent component crashes
      setServices([]);
      setThreats([]);
      setStatistics(null);
    } finally {
      setLoading(false);
    }
  };

  const performHealthCheck = async () => {
    setHealthCheckLoading(true);
    try {
      await api.post('/security/cybersecurity/health-check/');
      await loadDashboardData();
    } catch (error) {
      console.error('Health check failed:', error);
    } finally {
      setHealthCheckLoading(false);
    }
  };

  const getServiceTypeIcon = (serviceType: string) => {
    switch (serviceType) {
      case 'IDS': return <BugReport />;
      case 'IPS': return <Block />;
      case 'SIEM': return <Security />;
      case 'MONITORING': return <MonitorHeart />;
      case 'ALERTING': return <Notifications />;
      case 'WAF': return <Shield />;
      default: return <NetworkCheck />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'success';
      case 'INACTIVE': return 'error';
      case 'MAINTENANCE': return 'warning';
      case 'ERROR': return 'error';
      case 'INITIALIZING': return 'info';
      default: return 'default';
    }
  };

  const getHealthStatusColor = (healthStatus: string) => {
    switch (healthStatus) {
      case 'HEALTHY': return 'success';
      case 'WARNING': return 'warning';
      case 'CRITICAL': return 'error';
      case 'UNKNOWN': return 'default';
      default: return 'default';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return theme.palette.error.main;
      case 'HIGH': return theme.palette.warning.main;
      case 'MEDIUM': return theme.palette.info.main;
      case 'LOW': return theme.palette.success.main;
      default: return theme.palette.grey[500];
    }
  };

  const openServiceDetail = (service: CyberSecurityService) => {
    setSelectedService(service);
    setServiceDetailOpen(true);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert
          severity="error"
          action={
            <Button
              color="inherit"
              size="small"
              onClick={() => {
                setLoading(true);
                loadDashboardData();
              }}
            >
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" component="h1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Security color="primary" />
            Cybersecurity Management
          </Typography>
          <Typography variant="body2" color="textSecondary" sx={{ mt: 0.5 }}>
            🟢 Live monitoring • Last updated: {lastUpdate.toLocaleTimeString()}
          </Typography>
        </Box>
        <Box>
          <Button
            variant={alertsEnabled ? "contained" : "outlined"}
            color={alertsEnabled ? "success" : "inherit"}
            size="small"
            onClick={() => setAlertsEnabled(!alertsEnabled)}
            sx={{ mr: 1 }}
          >
            🔔 {alertsEnabled ? 'ON' : 'OFF'}
          </Button>
          <Button
            variant="outlined"
            startIcon={healthCheckLoading ? <CircularProgress size={20} /> : <Refresh />}
            onClick={performHealthCheck}
            disabled={healthCheckLoading}
            sx={{ mr: 1 }}
          >
            Health Check
          </Button>
          <Button
            variant="contained"
            startIcon={<Settings />}
            onClick={() => {/* Navigate to settings */}}
          >
            Settings
          </Button>
        </Box>
      </Box>

      {/* Statistics Cards */}
      {statistics ? (
        <Grid container spacing={3} mb={3}>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="body2">
                      Total Services
                    </Typography>
                    <Typography variant="h4">
                      {statistics.total_services}
                    </Typography>
                  </Box>
                  <NetworkCheck color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="body2">
                      Active Services
                    </Typography>
                    <Typography variant="h4" color="success.main">
                      {statistics.active_services}
                    </Typography>
                  </Box>
                  <CheckCircle color="success" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="body2">
                      Healthy Services
                    </Typography>
                    <Typography variant="h4" color="success.main">
                      {statistics.healthy_services}
                    </Typography>
                  </Box>
                  <MonitorHeart color="success" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="body2">
                      Threats (24h)
                    </Typography>
                    <Typography variant="h4" color="warning.main">
                      {statistics.threats_detected_24h}
                    </Typography>
                  </Box>
                  <Warning color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="body2">
                      Blocked (24h)
                    </Typography>
                    <Typography variant="h4" color="error.main">
                      {statistics.threats_blocked_24h}
                    </Typography>
                  </Box>
                  <Block color="error" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="body2">
                      Critical (24h)
                    </Typography>
                    <Typography variant="h4" color="error.main">
                      {statistics.critical_threats_24h}
                    </Typography>
                  </Box>
                  <Error color="error" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      ) : (
        <Alert severity="info" sx={{ mb: 3 }}>
          No statistics available. The cybersecurity services may be initializing.
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Services Overview */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Cybersecurity Services
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Service</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Health</TableCell>
                      <TableCell>Uptime</TableCell>
                      <TableCell>Response</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {services.length > 0 ? services.map((service) => (
                      <TableRow key={service.id}>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            {getServiceTypeIcon(service.service_type)}
                            <Box>
                              {service.config_data?.public_url ? (
                                <Typography
                                  variant="body2"
                                  fontWeight="medium"
                                  component="a"
                                  href={service.config_data.public_url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  sx={{
                                    color: 'primary.main',
                                    textDecoration: 'none',
                                    '&:hover': {
                                      textDecoration: 'underline'
                                    }
                                  }}
                                >
                                  {service.name} ↗
                                </Typography>
                              ) : (
                                <Typography variant="body2" fontWeight="medium">
                                  {service.name}
                                </Typography>
                              )}
                              {service.endpoint_url && (
                                <Typography
                                  variant="caption"
                                  component="a"
                                  href={service.endpoint_url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  sx={{
                                    color: 'primary.main',
                                    textDecoration: 'none',
                                    '&:hover': {
                                      textDecoration: 'underline',
                                      color: 'primary.dark'
                                    }
                                  }}
                                >
                                  🔗 {service.endpoint_url}
                                </Typography>
                              )}
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={service.service_type}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={service.status}
                            size="small"
                            color={getStatusColor(service.status) as any}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={service.health_status}
                            size="small"
                            color={getHealthStatusColor(service.health_status) as any}
                          />
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2">
                              {service.uptime_percentage.toFixed(1)}%
                            </Typography>
                            <LinearProgress
                              variant="determinate"
                              value={service.uptime_percentage}
                              sx={{ mt: 0.5, height: 4 }}
                            />
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {service.response_time_ms}ms
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box display="flex" gap={0.5}>
                            <Tooltip title="View Details">
                              <IconButton
                                size="small"
                                onClick={() => openServiceDetail(service)}
                              >
                                <Visibility />
                              </IconButton>
                            </Tooltip>
                            {service.endpoint_url && (
                              <Tooltip title="Open Dashboard">
                                <IconButton
                                  size="small"
                                  component="a"
                                  href={service.endpoint_url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  sx={{ color: 'primary.main' }}
                                >
                                  <OpenInNew />
                                </IconButton>
                              </Tooltip>
                            )}
                          </Box>
                        </TableCell>
                      </TableRow>
                    )) : (
                      <TableRow>
                        <TableCell colSpan={7} align="center">
                          <Typography variant="body2" color="textSecondary">
                            No cybersecurity services found. Please check your configuration.
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Threats */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  Recent Threats
                </Typography>
                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <InputLabel>Filter</InputLabel>
                  <Select
                    value={threatFilter}
                    label="Filter"
                    onChange={(e) => setThreatFilter(e.target.value)}
                  >
                    <MenuItem value="all">All</MenuItem>
                    <MenuItem value="HIGH">High</MenuItem>
                    <MenuItem value="CRITICAL">Critical</MenuItem>
                    <MenuItem value="MEDIUM">Medium</MenuItem>
                    <MenuItem value="LOW">Low</MenuItem>
                  </Select>
                </FormControl>
              </Box>
              <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>
                {threats.length > 0 ? threats
                  .filter(threat => threatFilter === 'all' || threat.severity === threatFilter)
                  .map((threat) => (
                  <Box
                    key={threat.id}
                    sx={{
                      p: 2,
                      mb: 1,
                      border: 1,
                      borderColor: 'divider',
                      borderRadius: 1,
                      borderLeft: 4,
                      borderLeftColor: getSeverityColor(threat.severity)
                    }}
                  >
                    <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                      <Typography variant="body2" fontWeight="medium">
                        {threat.threat_category.replace('_', ' ')}
                      </Typography>
                      <Chip
                        label={threat.severity}
                        size="small"
                        sx={{
                          backgroundColor: getSeverityColor(threat.severity),
                          color: 'white'
                        }}
                      />
                    </Box>
                    <Typography variant="caption" color="textSecondary" display="block">
                      Source: {threat.source_ip}
                    </Typography>
                    <Typography variant="caption" color="textSecondary" display="block">
                      Action: {threat.action_taken}
                    </Typography>
                    <Typography variant="caption" color="textSecondary" display="block">
                      Detected by: {threat.detecting_service}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      {new Date(threat.created_at).toLocaleString()}
                    </Typography>
                  </Box>
                )) : (
                  <Box sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="body2" color="textSecondary">
                      No recent threats detected. Your systems are secure.
                    </Typography>
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Performance Metrics */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                🚀 Performance Metrics
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h4" color="primary">
                      {services.filter(s => s.health_status === 'HEALTHY').length}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Healthy Services
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h4" color="success.main">
                      {services.length > 0 ? Math.round(services.reduce((acc, s) => acc + s.uptime_percentage, 0) / services.length) : 0}%
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Average Uptime
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h4" color="warning.main">
                      {services.length > 0 ? Math.round(services.reduce((acc, s) => acc + s.response_time_ms, 0) / services.length) : 0}ms
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Avg Response Time
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h4" color="error.main">
                      {threats.filter(t => t.severity === 'HIGH' || t.severity === 'CRITICAL').length}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Critical Threats
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Service Detail Dialog */}
      <Dialog
        open={serviceDetailOpen}
        onClose={() => setServiceDetailOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Service Details: {selectedService?.name}
        </DialogTitle>
        <DialogContent>
          {selectedService && (
            <Box>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Service Type</Typography>
                  <Typography variant="body1">{selectedService.service_type}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Status</Typography>
                  <Chip
                    label={selectedService.status}
                    size="small"
                    color={getStatusColor(selectedService.status) as any}
                  />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Health Status</Typography>
                  <Chip
                    label={selectedService.health_status}
                    size="small"
                    color={getHealthStatusColor(selectedService.health_status) as any}
                  />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Uptime</Typography>
                  <Typography variant="body1">{selectedService.uptime_percentage.toFixed(2)}%</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Response Time</Typography>
                  <Typography variant="body1">{selectedService.response_time_ms}ms</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Last Health Check</Typography>
                  <Typography variant="body1">
                    {selectedService.last_health_check ? 
                      new Date(selectedService.last_health_check).toLocaleString() : 
                      'Never'
                    }
                  </Typography>
                </Grid>
                {selectedService.endpoint_url && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="textSecondary">Endpoint URL</Typography>
                    <Typography
                      variant="body1"
                      component="a"
                      href={selectedService.endpoint_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      sx={{
                        color: 'primary.main',
                        textDecoration: 'none',
                        '&:hover': {
                          textDecoration: 'underline',
                          color: 'primary.dark'
                        }
                      }}
                    >
                      🔗 {selectedService.endpoint_url}
                    </Typography>
                  </Grid>
                )}
              </Grid>

              {/* Service Control Actions */}
              <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="h6" gutterBottom>
                  🎛️ Service Controls
                </Typography>
                <Box display="flex" gap={1} flexWrap="wrap">
                  {selectedService.endpoint_url && (
                    <Button
                      variant="contained"
                      size="small"
                      startIcon={<OpenInNew />}
                      component="a"
                      href={selectedService.endpoint_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      sx={{ mb: 1 }}
                    >
                      Open Dashboard
                    </Button>
                  )}
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<Refresh />}
                    onClick={() => {
                      // Restart service logic
                      alert(`Restarting ${selectedService.name}...`);
                    }}
                  >
                    Restart
                  </Button>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<Settings />}
                    onClick={() => {
                      // Configure service logic
                      alert(`Opening configuration for ${selectedService.name}...`);
                    }}
                  >
                    Configure
                  </Button>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<Visibility />}
                    onClick={() => {
                      // View logs logic
                      alert(`Opening logs for ${selectedService.name}...`);
                    }}
                  >
                    View Logs
                  </Button>
                  {selectedService.config_data?.public_url && (
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<NetworkCheck />}
                      component="a"
                      href={selectedService.config_data.public_url}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Open Dashboard
                    </Button>
                  )}
                </Box>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setServiceDetailOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CyberSecurityDashboard;
