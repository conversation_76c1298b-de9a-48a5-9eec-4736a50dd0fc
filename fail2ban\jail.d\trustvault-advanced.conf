[DEFAULT]
# Working Fail2Ban Configuration for TrustVault
bantime = 3600
findtime = 600
maxretry = 5
backend = auto
enabled = false

# Rate Limiting Protection Only
[nginx-limit-req]
enabled = true
port = http,https
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
maxretry = 10
findtime = 300
bantime = 1800
action = iptables-multiport[name=nginx-limit-req, port="http,https", protocol=tcp]

