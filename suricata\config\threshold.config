# Thresholding:
#
# This feature is used to reduce the number of logged alerts for noisy rules.
# Thresholding commands limit the number of times a particular event is logged
# during a specified time interval.
#
# The syntax is the following:
#
# threshold gen_id <gen_id>, sig_id <sig_id>, type <limit|threshold|both>, track <by_src|by_dst>, count <n>, seconds <t>
#
# event_filter gen_id <gen_id>, sig_id <sig_id>, type <limit|threshold|both>, track <by_src|by_dst>, count <n>, seconds <t>
#
# suppress gen_id <gid>, sig_id <sid>
# suppress gen_id <gid>, sig_id <sid>, track <by_src|by_dst>, ip <ip|subnet>
#
# The options are documented at https://docs.suricata.io/en/latest/configuration/global-thresholds.html
#
# Please note that thresholding can also be set inside a signature. The interaction between rule based thresholds
# and global thresholds is documented here:
# https://docs.suricata.io/en/latest/configuration/global-thresholds.html#global-thresholds-vs-rule-thresholds

# Limit to 10 alerts every 10 seconds for each source host
#threshold gen_id 0, sig_id 0, type limit, track by_src, count 10, seconds 10

# Limit to 1 alert every 10 seconds for signature with sid 2404000 per destination host
#threshold gen_id 1, sig_id 2404000, type limit, track by_dst, count 1, seconds 10

# Avoid to alert on f-secure update
# Example taken from https://blog.inliniac.net/2012/03/07/f-secure-av-updates-and-suricata-ips/
#suppress gen_id 1, sig_id 2009557, track by_src, ip **************/25
#suppress gen_id 1, sig_id 2012086, track by_src, ip **************/25
#suppress gen_id 1, sig_id 2003614, track by_src, ip **************/25
