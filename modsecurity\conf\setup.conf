# Note: the plugin rules will be uncommented when the container starts,
# depending on whether the respective files exist. This works around
# the issue that ModSecurity doesn't support optional includes on NGiNX.

# Allow custom rules to be specified in:
# /opt/modsecurity/rules/{before,after}-crs/*.conf

Include /etc/modsecurity.d/modsecurity.conf
Include /etc/modsecurity.d/modsecurity-override.conf

Include /etc/modsecurity.d/owasp-crs/crs-setup.conf

Include /etc/modsecurity.d/owasp-crs/plugins/*-config.conf
Include /etc/modsecurity.d/owasp-crs/plugins/*-before.conf

Include /etc/modsecurity.d/owasp-crs/rules/*.conf

Include /etc/modsecurity.d/owasp-crs/plugins/*-after.conf
