// TrustVault - Main App Component

import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box, CircularProgress } from '@mui/material';
import { Helmet } from 'react-helmet-async';

// Store
import { useAuthStore } from './store/authStore';

// Contexts
import { CustomThemeProvider } from './contexts/ThemeContext';

// Components
import Layout from './components/Layout/Layout';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import ErrorBoundary from './components/ErrorBoundary';

// Pages
import LoginPage from './pages/Auth/LoginPage';
import RegisterPage from './pages/Auth/RegisterPage';
import DashboardPage from './pages/Dashboard/DashboardPage';
import PortfoliosPage from './pages/Portfolio/PortfoliosPage';
import PortfolioDetailPage from './pages/Portfolio/PortfolioDetailPage';
import CreatePortfolioPage from './pages/Portfolio/CreatePortfolioPage';
import AddHoldingPage from './pages/Portfolio/AddHoldingPage';
import TransactionsPage from './pages/Portfolio/TransactionsPage';
import AnalyticsPage from './pages/Portfolio/AnalyticsPage';
import SecurityPage from './pages/Security/SecurityPage';
import CyberSecurityPage from './pages/CyberSecurity/CyberSecurityPage';
import ProfilePage from './pages/Profile/ProfilePage';
import SettingsPage from './pages/Settings/SettingsPage';
import AlertsPage from './pages/Alerts/AlertsPage';
import ReportsPage from './pages/Reports/ReportsPage';
import NotificationTestPage from './pages/NotificationTest/NotificationTestPage';
import DebugPage from './pages/Debug/DebugPage';
import NotFoundPage from './pages/NotFoundPage';

// Utils
import { validateEnvironment } from './utils/security';
import { CyberSecurityDashboard } from './components/CyberSecurity';

const App: React.FC = () => {
  const { isAuthenticated, isLoading, loadUser } = useAuthStore();

  useEffect(() => {
    // Validate environment security
    validateEnvironment();

    // Load user if token exists
    loadUser();
  }, [loadUser]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <CircularProgress size={40} />
      </Box>
    );
  }

  return (
    <CustomThemeProvider>
      <ErrorBoundary>
      <Helmet>
        <title>TrustVault - Secure Portfolio Management</title>
        <meta name="description" content="Secure portfolio management platform with advanced cybersecurity features" />
        <meta name="keywords" content="portfolio, investment, security, cybersecurity, finance" />
        <meta name="author" content="TrustVault Team" />
        
        {/* Security meta tags */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
        
        {/* Preconnect to API */}
        <link rel="preconnect" href={process.env.REACT_APP_API_URL || '/api'} />
      </Helmet>

      <Routes>
        {/* Public routes */}
        <Route
          path="/login"
          element={
            isAuthenticated ? (
              <Navigate to="/dashboard" replace />
            ) : (
              <LoginPage />
            )
          }
        />
        <Route
          path="/register"
          element={
            isAuthenticated ? (
              <Navigate to="/dashboard" replace />
            ) : (
              <RegisterPage />
            )
          }
        />



        {/* Protected routes */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<DashboardPage />} />
          <Route path="portfolios" element={<PortfoliosPage />} />
          <Route path="portfolios/create" element={<CreatePortfolioPage />} />
          <Route path="portfolios/:id" element={<PortfolioDetailPage />} />
          <Route path="portfolios/:id/add-holding" element={<AddHoldingPage />} />
          <Route path="portfolios/:id/transactions" element={<TransactionsPage />} />
          <Route path="portfolios/:id/analytics" element={<AnalyticsPage />} />
          <Route path="alerts" element={<AlertsPage />} />
          <Route path="reports" element={<ReportsPage />} />
          <Route path="notifications-test" element={<NotificationTestPage />} />
          <Route path="debug" element={<DebugPage />} />
          <Route path="security" element={<SecurityPage />} />
          <Route path="cybersecurity" element={<CyberSecurityPage />} />
          <Route path="profile" element={<ProfilePage />} />
          <Route path="settings" element={<SettingsPage />} />
        </Route>

        {/* 404 page */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
      </ErrorBoundary>
    </CustomThemeProvider>
  );
};

export default App;
