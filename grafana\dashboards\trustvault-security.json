{
  "dashboard": {
    "id": null,
    "title": "TrustVault - Security Operations Center",
    "tags": ["trustvault", "security", "monitoring", "soc"],
    "style": "dark",
    "timezone": "browser",
    "refresh": "5s",
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "panels": [
      {
        "id": 1,
        "title": "🛡️ Security Services Status",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"nginx\"}",
            "legendFormat": "Nginx Proxy",
            "refId": "A"
          },
          {
            "expr": "up{job=\"suricata\"}",
            "legendFormat": "Suricata IDS",
            "refId": "B"
          },
          {
            "expr": "up{job=\"fail2ban\"}",
            "legendFormat": "Fail2Ban IPS",
            "refId": "C"
          },
          {
            "expr": "up{job=\"wazuh-manager\"}",
            "legendFormat": "Wazuh SIEM",
            "refId": "D"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {"mode": "thresholds"},
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "green", "value": 1}
              ]
            },
            "mappings": [
              {
                "options": {
                  "0": {"text": "DOWN", "color": "red"},
                  "1": {"text": "UP", "color": "green"}
                },
                "type": "value"
              }
            ]
          }
        },
        "gridPos": {"h": 4, "w": 24, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "🚨 Real-Time Security Alerts",
        "type": "logs",
        "targets": [
          {
            "expr": "{job=\"nginx\"} |= \"403\" or \"429\" or \"444\"",
            "legendFormat": "Security Blocks",
            "refId": "A"
          }
        ],
        "options": {
          "showTime": true,
          "showLabels": true,
          "sortOrder": "Descending"
        },
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}
      },
      {
        "id": 3,
        "title": "📊 Traffic Analysis",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(nginx_http_requests_total[5m])",
            "legendFormat": "Requests/sec",
            "refId": "A"
          },
          {
            "expr": "rate(nginx_http_requests_total{status=~\"4..\"}[5m])",
            "legendFormat": "4xx Errors/sec",
            "refId": "B"
          },
          {
            "expr": "rate(nginx_http_requests_total{status=~\"5..\"}[5m])",
            "legendFormat": "5xx Errors/sec",
            "refId": "C"
          }
        ],
        "yAxes": [
          {
            "label": "Requests per second",
            "min": 0
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}
      },
      {
        "id": 4,
        "title": "🔒 Security Metrics Overview",
        "type": "singlestat",
        "targets": [
          {
            "expr": "sum(rate(nginx_http_requests_total{status=\"403\"}[1h]))",
            "legendFormat": "Blocked Requests/hour",
            "refId": "A"
          }
        ],
        "valueName": "current",
        "format": "short",
        "colorBackground": true,
        "thresholds": "0,10,50",
        "colors": ["green", "yellow", "red"],
        "gridPos": {"h": 4, "w": 6, "x": 0, "y": 12}
      },
      {
        "id": 5,
        "title": "🌐 Geographic Threat Map",
        "type": "worldmap",
        "targets": [
          {
            "expr": "sum by (country) (geoip_country_requests_total)",
            "legendFormat": "{{country}}",
            "refId": "A"
          }
        ],
        "circleMaxSize": 30,
        "circleMinSize": 2,
        "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"],
        "gridPos": {"h": 8, "w": 12, "x": 6, "y": 12}
      },
      {
        "id": 6,
        "title": "⚡ Response Time Analysis",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(nginx_http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile",
            "refId": "A"
          },
          {
            "expr": "histogram_quantile(0.50, rate(nginx_http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile",
            "refId": "B"
          }
        ],
        "yAxes": [
          {
            "label": "Response Time (seconds)",
            "min": 0
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}
      },
      {
        "id": 7,
        "title": "🔍 Top Attack Patterns",
        "type": "table",
        "targets": [
          {
            "expr": "topk(10, sum by (attack_type) (rate(security_events_total[1h])))",
            "legendFormat": "{{attack_type}}",
            "refId": "A"
          }
        ],
        "columns": [
          {"text": "Attack Type", "value": "attack_type"},
          {"text": "Count", "value": "Value"}
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}
      }
    ],
    "templating": {
      "list": [
        {
          "name": "instance",
          "type": "query",
          "query": "label_values(up, instance)",
          "refresh": 1,
          "includeAll": true,
          "multi": true
        }
      ]
    },
    "annotations": {
      "list": [
        {
          "name": "Security Events",
          "datasource": "Prometheus",
          "expr": "security_alert_total",
          "titleFormat": "Security Alert",
          "textFormat": "{{alert_type}}: {{description}}"
        }
      ]
    }
  }
}
      {
        "id": 2,
        "title": "Failed Login Attempts",
        "type": "graph",
        "targets": [
          {
            "expr": "increase(django_login_failures_total[5m])",
            "legendFormat": "Failed Logins"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}
      },
      {
        "id": 3,
        "title": "Blocked IPs (Fail2Ban)",
        "type": "stat",
        "targets": [
          {
            "expr": "fail2ban_banned_ips_total",
            "legendFormat": "Banned IPs"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}
      },
      {
        "id": 4,
        "title": "WAF Blocked Requests",
        "type": "graph",
        "targets": [
          {
            "expr": "increase(modsecurity_blocked_requests_total[5m])",
            "legendFormat": "Blocked Requests"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}
      },
      {
        "id": 5,
        "title": "Suricata Alerts",
        "type": "graph",
        "targets": [
          {
            "expr": "increase(suricata_alerts_total[5m])",
            "legendFormat": "IDS Alerts"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}
      },
      {
        "id": 6,
        "title": "SSL Certificate Status",
        "type": "stat",
        "targets": [
          {
            "expr": "ssl_certificate_expiry_days",
            "legendFormat": "Days Until Expiry"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {"mode": "thresholds"},
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "yellow", "value": 30},
                {"color": "green", "value": 90}
              ]
            }
          }
        },
        "gridPos": {"h": 6, "w": 12, "x": 0, "y": 22}
      },
      {
        "id": 7,
        "title": "Active User Sessions",
        "type": "stat",
        "targets": [
          {
            "expr": "django_active_sessions_total",
            "legendFormat": "Active Sessions"
          }
        ],
        "gridPos": {"h": 6, "w": 12, "x": 12, "y": 22}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "10s"
  },
  "overwrite": true
}
