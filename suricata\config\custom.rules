# TrustVault Custom Suricata Rules

# Detect SQL injection attempts
alert http any any -> any any (msg:"SQL Injection Attempt"; content:"union"; nocase; content:"select"; nocase; sid:1000001; rev:1;)
alert http any any -> any any (msg:"SQL Injection Attempt - OR 1=1"; content:"or 1=1"; nocase; sid:1000002; rev:1;)

# Detect XSS attempts
alert http any any -> any any (msg:"XSS Attempt - Script Tag"; content:"<script"; nocase; sid:1000003; rev:1;)
alert http any any -> any any (msg:"XSS Attempt - JavaScript"; content:"javascript:"; nocase; sid:1000004; rev:1;)

# Detect path traversal
alert http any any -> any any (msg:"Path Traversal Attempt"; content:"../"; sid:1000005; rev:1;)
alert http any any -> any any (msg:"Path Traversal Attempt - Windows"; content:"..\\"; sid:1000006; rev:1;)

# Detect brute force attempts
alert http any any -> any any (msg:"Brute Force Login Attempt"; content:"POST"; http_method; content:"/login"; http_uri; threshold: type both, track by_src, count 10, seconds 60; sid:1000007; rev:1;)

# Detect suspicious user agents
alert http any any -> any any (msg:"Suspicious User Agent - SQLMap"; content:"sqlmap"; http_user_agent; nocase; sid:1000008; rev:1;)
alert http any any -> any any (msg:"Suspicious User Agent - Nikto"; content:"nikto"; http_user_agent; nocase; sid:1000009; rev:1;)
