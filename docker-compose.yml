

networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
  security:
    driver: bridge
  database:
    driver: bridge
    internal: true

services:
  # ============================================================================
  # SECURITY PERIMETER - WAF & REVERSE PROXY
  # ============================================================================

  nginx:
    build: ./nginx
    container_name: trustvault-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
      - ./nginx/html:/usr/share/nginx/html
    networks:
      - frontend
      - backend
      - security
      - database
    depends_on:
      - django
      - react
    restart: unless-stopped
    environment:
      - NGINX_ENVSUBST_TEMPLATE_SUFFIX=.template
    labels:
      - "com.trustvault.service=nginx"
      - "com.trustvault.security.level=high"

  # ModSecurity WAF
  modsecurity:
    image: owasp/modsecurity-crs:nginx
    container_name: trustvault-waf
    # Port removed - access only through nginx
    volumes:
      - ./logs/modsecurity:/var/log/modsec
    networks:
      - frontend
      - backend
    environment:
      - PARANOIA=1
      - ANOMALY_INBOUND=5
      - ANOMALY_OUTBOUND=4
      - BLOCKING_PARANOIA=1
      - BACKEND=http://trustvault-django:8000
    restart: unless-stopped

  # Fail2Ban for DDoS Protection
  fail2ban:
    image: crazymax/fail2ban:latest
    container_name: trustvault-fail2ban
    network_mode: "host"
    cap_add:
      - NET_ADMIN
      - NET_RAW
    volumes:
      - ./fail2ban:/data
      - ./logs:/var/log:ro
    environment:
      - TZ=Europe/Paris
      - F2B_LOG_LEVEL=INFO
    restart: unless-stopped

  # ============================================================================
  # APPLICATION LAYER
  # ============================================================================

  django:
    build: ./backend
    container_name: trustvault-django
    # Port removed - access only through nginx
    volumes:
      - ./backend:/app
      - django_media:/app/media
      - django_static:/app/staticfiles
      - ./logs/django:/app/logs
    networks:
      - backend
      - database
      - security
    environment:
      - DEBUG=${DEBUG:-False}
      - DJANGO_SECRET_KEY=${DJANGO_SECRET_KEY}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - VAULT_ROOT_TOKEN=${VAULT_ROOT_TOKEN}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - POSTGRES_DB=trustvault
      - POSTGRES_USER=trustvault
      - DB_HOST=postgres
      - DB_PORT=5432
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - VAULT_URL=http://vault:8200
      - ALLOWED_HOSTS=localhost,127.0.0.1,django,trustvault.local,api.trustvault.local
    depends_on:
      - postgres
      - redis
      - vault
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "com.trustvault.service=django"
      - "com.trustvault.security.level=high"

  react:
    build:
      context: ./frontend
      args:
        REACT_APP_API_URL: /api/v1
        REACT_APP_ENVIRONMENT: production
    container_name: trustvault-react
    # Port removed - access only through nginx
    networks:
      - frontend
      - backend
    environment:
      - REACT_APP_API_URL=/api/v1
      - REACT_APP_ENVIRONMENT=production
    restart: unless-stopped

  # Celery Worker
  celery-worker:
    build: ./backend
    container_name: trustvault-celery-worker
    command: celery -A trustvault worker -l info
    volumes:
      - ./backend:/app
      - ./logs/celery:/app/logs
    networks:
      - backend
      - database
    environment:
      - DEBUG=${DEBUG:-False}
      - DJANGO_SECRET_KEY=${DJANGO_SECRET_KEY}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - VAULT_ROOT_TOKEN=${VAULT_ROOT_TOKEN}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - POSTGRES_DB=trustvault
      - POSTGRES_USER=trustvault
      - DB_HOST=postgres
      - DB_PORT=5432
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - ALLOWED_HOSTS=localhost,127.0.0.1,django,trustvault.local,api.trustvault.local
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Celery Beat (Scheduler)
  celery-beat:
    build: ./backend
    container_name: trustvault-celery-beat
    command: celery -A trustvault beat -l info
    volumes:
      - ./backend:/app
      - ./logs/celery:/app/logs
    networks:
      - backend
      - database
    environment:
      - DEBUG=${DEBUG:-False}
      - DJANGO_SECRET_KEY=${DJANGO_SECRET_KEY}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - POSTGRES_DB=trustvault
      - POSTGRES_USER=trustvault
      - DB_HOST=postgres
      - DB_PORT=5432
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - ALLOWED_HOSTS=localhost,127.0.0.1,django,trustvault.local,api.trustvault.local
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
  # ============================================================================
  # DATA LAYER - ENCRYPTED DATABASES
  # ============================================================================

  postgres:
    image: postgres:15-alpine
    container_name: trustvault-postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init:/docker-entrypoint-initdb.d
    networks:
      - database
    environment:
      - POSTGRES_DB=trustvault
      - POSTGRES_USER=trustvault
      - POSTGRES_PASSWORD_FILE=/run/secrets/db_password
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    secrets:
      - db_password
    command: >
      postgres
      -c ssl=off
      -c log_statement=all
      -c log_destination=stderr
      -c shared_preload_libraries=pg_stat_statements
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    labels:
      - "com.trustvault.service=postgres"
      - "com.trustvault.security.level=critical"

  redis:
    image: redis:7-alpine
    container_name: trustvault-redis
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - backend
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass ${REDIS_PASSWORD}
    restart: unless-stopped

  # HashiCorp Vault for Secrets Management
  vault:
    image: hashicorp/vault:latest
    container_name: trustvault-vault
    ports:
      - "8200:8200"  # Expose on port 8200
    cap_add:
      - IPC_LOCK
    volumes:
      - vault_data:/vault/data
      - ./vault/config:/vault/config
    networks:
      - backend
    environment:
      - VAULT_DEV_ROOT_TOKEN_ID=${VAULT_ROOT_TOKEN}
      - VAULT_DEV_LISTEN_ADDRESS=0.0.0.0:8200
    command: vault server -config=/vault/config/vault.hcl
    restart: unless-stopped
  # ============================================================================
  # SECURITY & MONITORING - SIEM/SOC
  # ============================================================================



  # Wazuh Indexer (OpenSearch) - Simplified Configuration
  wazuh-indexer:
    image: wazuh/wazuh-indexer:4.7.0
    container_name: trustvault-wazuh-indexer
    hostname: wazuh.indexer
    # Port removed - access through nginx proxy or internal network only
    volumes:
      - wazuh_indexer_data:/var/lib/wazuh-indexer
      - ./wazuh/indexer/opensearch.yml:/usr/share/wazuh-indexer/opensearch.yml:ro
    networks:
      - security
    environment:
      - "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m"
      - bootstrap.memory_lock=true
      - "DISABLE_INSTALL_DEMO_CONFIG=true"
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    restart: unless-stopped

  # Wazuh Manager - Simplified Configuration
  wazuh-manager:
    image: wazuh/wazuh-manager:4.7.0
    container_name: trustvault-wazuh-manager
    hostname: wazuh.manager
    # Port removed - access through nginx proxy or internal network only
    volumes:
      - wazuh_data:/var/ossec/data
      - ./wazuh/config/ossec.conf:/wazuh-config-mount/etc/ossec.conf:ro
      - ./logs:/var/log/external:ro
    networks:
      - security
    environment:
      - INDEXER_URL=http://trustvault-wazuh-indexer:9200
      - INDEXER_USERNAME=""
      - INDEXER_PASSWORD=""
      - FILEBEAT_SSL_VERIFICATION_MODE=none
    depends_on:
      - wazuh-indexer
    restart: unless-stopped

  # Wazuh Dashboard - Simplified Configuration
  wazuh-dashboard:
    image: wazuh/wazuh-dashboard:4.7.0
    container_name: trustvault-wazuh-dashboard
    hostname: wazuh.dashboard
    ports:
      - "5601:5601"  # Expose on port 5601
    volumes:
      - ./wazuh/dashboard/opensearch_dashboards.yml:/usr/share/wazuh-dashboard/config/opensearch_dashboards.yml:ro
    networks:
      - security
      - frontend
    environment:
      - INDEXER_URL=http://trustvault-wazuh-indexer:9200
      - INDEXER_USERNAME=""
      - INDEXER_PASSWORD=""
      - WAZUH_API_URL=https://trustvault-wazuh-manager:55000
      - API_USERNAME=wazuh-wui
      - API_PASSWORD=wazuh-wui
    depends_on:
      - wazuh-indexer
      - wazuh-manager
    restart: unless-stopped

  # Suricata IDS/IPS
  suricata:
    image: jasonish/suricata:latest
    container_name: trustvault-suricata
    network_mode: host
    cap_add:
      - NET_ADMIN
      - SYS_NICE
    volumes:
      - ./suricata/config:/etc/suricata
      - ./suricata/rules:/var/lib/suricata/rules
      - ./logs/suricata:/var/log/suricata
    command: >
      suricata -c /etc/suricata/suricata.yaml -i eth0 -v
    restart: unless-stopped
  # ============================================================================
  # MONITORING & METRICS
  # ============================================================================

  prometheus:
    image: prom/prometheus:latest
    container_name: trustvault-prometheus
    ports:
      - "9090:9090"  # Expose on port 9090
    volumes:
      - prometheus_data:/prometheus
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./prometheus/security_rules.yml:/etc/prometheus/security_rules.yml
    networks:
      - security
      - backend
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: trustvault-grafana
    ports:
      - "3001:3000"  # Expose on port 3001
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - security
      - frontend
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
    restart: unless-stopped

  # AlertManager for notifications
  alertmanager:
    image: prom/alertmanager:latest
    container_name: trustvault-alertmanager
    ports:
      - "9093:9093"  # Expose on port 9093
    volumes:
      - alertmanager_data:/alertmanager
      - ./alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
    networks:
      - security
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
    restart: unless-stopped

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: trustvault-node-exporter
    # Port removed - internal access only
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    networks:
      - security
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    restart: unless-stopped

  # cAdvisor for container metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: trustvault-cadvisor
    # Port removed - internal access only
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    networks:
      - security
    privileged: true
    devices:
      - /dev/kmsg
    restart: unless-stopped

  # PostgreSQL Exporter
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: trustvault-postgres-exporter
    # Port removed - internal access only
    networks:
      - database
      - security
    environment:
      - DATA_SOURCE_NAME=postgresql://trustvault:${DB_PASSWORD}@postgres:5432/trustvault?sslmode=disable
    depends_on:
      - postgres
    restart: unless-stopped

  # Redis Exporter
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: trustvault-redis-exporter
    # Port removed - internal access only
    networks:
      - backend
      - security
    environment:
      - REDIS_ADDR=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    depends_on:
      - redis
    restart: unless-stopped

  # Nginx Exporter
  nginx-exporter:
    image: nginx/nginx-prometheus-exporter:latest
    container_name: trustvault-nginx-exporter
    # Port removed - internal access only
    networks:
      - frontend
      - security
    command:
      - '-nginx.scrape-uri=http://nginx:8080/nginx_status'
    depends_on:
      - nginx
    restart: unless-stopped

  # Blackbox Exporter for endpoint monitoring
  blackbox-exporter:
    image: prom/blackbox-exporter:latest
    container_name: trustvault-blackbox-exporter
    # Port removed - internal access only
    networks:
      - security
      - frontend
    restart: unless-stopped





# ============================================================================
# VOLUMES
# ============================================================================

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

  vault_data:
    driver: local
  vault_logs:
    driver: local
  wazuh_data:
    driver: local
  wazuh_indexer_data:
    driver: local

  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  alertmanager_data:
    driver: local
  django_media:
    driver: local
  django_static:
    driver: local

secrets:
  django_secret:
    file: ./secrets/django_secret.txt
  db_password:
    file: ./secrets/db_password.txt
