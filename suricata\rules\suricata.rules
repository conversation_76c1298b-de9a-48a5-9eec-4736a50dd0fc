# TrustVault - Suricata IDS Rules
# Custom security rules for financial portfolio management system

# ============================================================================
# NETWORK SECURITY RULES
# ============================================================================

# Detect SQL Injection Attempts
alert http any any -> $HOME_NET any (msg:"TrustVault: SQL Injection Attempt"; flow:established,to_server; content:"union"; nocase; content:"select"; nocase; distance:0; within:100; classtype:web-application-attack; sid:1000001; rev:1;)
alert http any any -> $HOME_NET any (msg:"TrustVault: SQL Injection - OR 1=1"; flow:established,to_server; content:"or"; nocase; content:"1=1"; nocase; distance:0; within:20; classtype:web-application-attack; sid:1000002; rev:1;)
alert http any any -> $HOME_NET any (msg:"TrustVault: SQL Injection - DROP TABLE"; flow:established,to_server; content:"drop"; nocase; content:"table"; nocase; distance:0; within:20; classtype:web-application-attack; sid:1000003; rev:1;)

# Detect XSS Attempts
alert http any any -> $HOME_NET any (msg:"TrustVault: XSS Attempt - Script Tag"; flow:established,to_server; content:"<script"; nocase; classtype:web-application-attack; sid:1000004; rev:1;)
alert http any any -> $HOME_NET any (msg:"TrustVault: XSS Attempt - JavaScript"; flow:established,to_server; content:"javascript:"; nocase; classtype:web-application-attack; sid:1000005; rev:1;)
alert http any any -> $HOME_NET any (msg:"TrustVault: XSS Attempt - OnLoad"; flow:established,to_server; content:"onload"; nocase; classtype:web-application-attack; sid:1000006; rev:1;)

# Detect Path Traversal
alert http any any -> $HOME_NET any (msg:"TrustVault: Path Traversal Attempt"; flow:established,to_server; content:"../"; classtype:web-application-attack; sid:1000007; rev:1;)
alert http any any -> $HOME_NET any (msg:"TrustVault: Path Traversal - etc/passwd"; flow:established,to_server; content:"/etc/passwd"; nocase; classtype:web-application-attack; sid:1000008; rev:1;)

# Detect Command Injection
alert http any any -> $HOME_NET any (msg:"TrustVault: Command Injection - Pipe"; flow:established,to_server; content:"|7C|"; classtype:web-application-attack; sid:1000009; rev:1;)
alert http any any -> $HOME_NET any (msg:"TrustVault: Command Injection - Semicolon"; flow:established,to_server; content:"|3B|"; content:"cat"; nocase; distance:0; within:20; classtype:web-application-attack; sid:1000010; rev:1;)

# ============================================================================
# FINANCIAL SECURITY RULES
# ============================================================================

# Detect Suspicious Financial Data Access
alert http any any -> $HOME_NET any (msg:"TrustVault: Suspicious Portfolio Access"; flow:established,to_server; content:"/api/portfolio"; content:"admin"; nocase; distance:0; classtype:policy-violation; sid:1000011; rev:1;)
alert http any any -> $HOME_NET any (msg:"TrustVault: Bulk Data Download Attempt"; flow:established,to_server; content:"export"; content:"all"; nocase; distance:0; within:50; classtype:policy-violation; sid:1000012; rev:1;)

# Detect Brute Force Attacks
alert http any any -> $HOME_NET any (msg:"TrustVault: Brute Force Login Attempt"; flow:established,to_server; content:"POST"; http_method; content:"/api/auth/login"; http_uri; threshold:type both, track by_src, count 5, seconds 60; classtype:attempted-user; sid:1000013; rev:1;)

# ============================================================================
# MALWARE AND THREAT DETECTION
# ============================================================================

# Detect Cryptocurrency Mining
alert http any any -> $HOME_NET any (msg:"TrustVault: Cryptocurrency Mining Script"; flow:established,to_server; content:"coinhive"; nocase; classtype:coin-mining; sid:1000014; rev:1;)
alert http any any -> $HOME_NET any (msg:"TrustVault: Mining Pool Connection"; flow:established,to_server; content:"stratum"; nocase; classtype:coin-mining; sid:1000015; rev:1;)

# Detect Suspicious User Agents
alert http any any -> $HOME_NET any (msg:"TrustVault: Suspicious Bot User Agent"; flow:established,to_server; content:"User-Agent|3A 20|"; content:"bot"; nocase; distance:0; within:50; classtype:bad-unknown; sid:1000016; rev:1;)
alert http any any -> $HOME_NET any (msg:"TrustVault: Scanner User Agent"; flow:established,to_server; content:"User-Agent|3A 20|"; content:"scan"; nocase; distance:0; within:50; classtype:bad-unknown; sid:1000017; rev:1;)

# ============================================================================
# NETWORK ANOMALY DETECTION
# ============================================================================

# Detect Port Scanning
alert tcp any any -> $HOME_NET any (msg:"TrustVault: Port Scan Detected"; flags:S,12; threshold:type both, track by_src, count 10, seconds 60; classtype:attempted-recon; sid:1000018; rev:1;)

# Detect Large File Uploads
alert http any any -> $HOME_NET any (msg:"TrustVault: Large File Upload"; flow:established,to_server; content:"Content-Length|3A 20|"; content:"|0D 0A|"; within:100; byte_test:8,>,10000000,0,string,dec,relative; classtype:policy-violation; sid:1000019; rev:1;)

# ============================================================================
# API SECURITY RULES
# ============================================================================

# Detect API Abuse
alert http any any -> $HOME_NET any (msg:"TrustVault: API Rate Limit Exceeded"; flow:established,to_server; content:"/api/"; threshold:type both, track by_src, count 100, seconds 60; classtype:attempted-dos; sid:1000020; rev:1;)
alert http any any -> $HOME_NET any (msg:"TrustVault: Unauthorized API Access"; flow:established,to_server; content:"/api/admin"; content:"Authorization"; nocase; distance:0; classtype:attempted-admin; sid:1000021; rev:1;)

# Detect JWT Token Manipulation
alert http any any -> $HOME_NET any (msg:"TrustVault: JWT Token Manipulation"; flow:established,to_server; content:"Authorization|3A 20|Bearer"; content:"eyJ"; distance:0; within:200; content:"admin"; nocase; distance:0; classtype:attempted-admin; sid:1000022; rev:1;)

# ============================================================================
# COMPLIANCE AND MONITORING
# ============================================================================

# Monitor Sensitive Data Access
alert http any any -> $HOME_NET any (msg:"TrustVault: PII Data Access"; flow:established,to_server; content:"/api/user"; content:"ssn"; nocase; distance:0; within:100; classtype:policy-violation; sid:1000023; rev:1;)
alert http any any -> $HOME_NET any (msg:"TrustVault: Financial Data Export"; flow:established,to_server; content:"export"; content:"financial"; nocase; distance:0; within:50; classtype:policy-violation; sid:1000024; rev:1;)

# Monitor Admin Actions
alert http any any -> $HOME_NET any (msg:"TrustVault: Admin Panel Access"; flow:established,to_server; content:"/admin"; http_uri; classtype:policy-violation; sid:1000025; rev:1;)

# ============================================================================
# EMERGING THREATS
# ============================================================================

# Detect Log4j Exploitation Attempts
alert http any any -> $HOME_NET any (msg:"TrustVault: Log4j Exploitation Attempt"; flow:established,to_server; content:"${jndi:"; nocase; classtype:attempted-admin; sid:1000026; rev:1;)

# Detect Shellshock Attempts
alert http any any -> $HOME_NET any (msg:"TrustVault: Shellshock Exploitation"; flow:established,to_server; content:"() {"; classtype:attempted-admin; sid:1000027; rev:1;)

# End of TrustVault Suricata Rules
