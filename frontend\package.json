{"name": "trustvault-frontend", "version": "1.0.0", "description": "TrustVault - Secure Portfolio Management Frontend", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.3.2", "@mui/icons-material": "^5.15.0", "@mui/material": "^5.15.0", "@mui/x-charts": "^6.18.1", "@mui/x-data-grid": "^6.18.1", "@mui/x-date-pickers": "^6.18.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^27.5.2", "axios": "^1.6.2", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "immer": "^10.0.3", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.4", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-otp-input": "^3.1.1", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "recharts": "^2.8.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "yup": "^1.4.0", "zustand": "^4.4.7"}, "devDependencies": {"@types/crypto-js": "^4.2.1", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.55.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-security": "^1.7.1", "prettier": "^3.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,json,css,md}", "type-check": "tsc --noEmit", "security-audit": "npm audit --audit-level moderate"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}