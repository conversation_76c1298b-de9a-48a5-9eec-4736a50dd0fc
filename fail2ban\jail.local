[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3
backend = auto

[nginx-http-auth]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 3

[nginx-limit-req]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10

[nginx-botsearch]
enabled = true
port = http,https
logpath = /var/log/nginx/access.log
maxretry = 2

[sshd]
enabled = false
port = ssh
logpath = /var/log/auth.log
maxretry = 3
