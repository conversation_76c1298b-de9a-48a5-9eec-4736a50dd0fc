#!/bin/bash

# TrustVault Startup Script
# This script starts all necessary services for the TrustVault application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${GREEN}TrustVault - Secure Portfolio Management System${NC}"
echo -e "${GREEN}=============================================${NC}"

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        echo -e "${RED}Error: Docker is not running. Please start Docker.${NC}"
        exit 1
    fi
}

# Function to stop all services
stop_services() {
    echo -e "${YELLOW}Stopping TrustVault services...${NC}"
    docker-compose down --remove-orphans
    echo -e "${GREEN}All services stopped.${NC}"
}

# Function to show service status
show_status() {
    echo -e "${CYAN}TrustVault Service Status:${NC}"
    docker-compose ps
}

# Function to show logs
show_logs() {
    echo -e "${CYAN}Showing TrustVault logs (press Ctrl+C to exit):${NC}"
    docker-compose logs -f
}

# Function to clean up
clean_services() {
    echo -e "${YELLOW}Cleaning up TrustVault...${NC}"
    docker-compose down --volumes --remove-orphans
    docker system prune -f
    echo -e "${GREEN}Cleanup completed.${NC}"
}

# Function to build and start services
start_services() {
    local build_images=$1
    
    echo -e "${CYAN}Starting TrustVault services...${NC}"
    
    # Check if Docker is running
    check_docker
    
    # Create logs directory if it doesn't exist
    mkdir -p logs
    
    if [ "$build_images" = "true" ]; then
        echo -e "${YELLOW}Building Docker images...${NC}"
        docker-compose build --no-cache
    fi
    
    # Start the services
    echo -e "${YELLOW}Starting services...${NC}"
    docker-compose up -d
    
    # Wait for services to be ready
    echo -e "${YELLOW}Waiting for services to start...${NC}"
    sleep 10
    
    # Check service status
    echo -e "${CYAN}Service Status:${NC}"
    docker-compose ps
    
    echo ""
    echo -e "${GREEN}TrustVault is starting up!${NC}"
    echo -e "${CYAN}Frontend: http://localhost:3000${NC}"
    echo -e "${CYAN}Backend API: http://localhost:8000${NC}"
    echo -e "${CYAN}Nginx Proxy: http://localhost:80${NC}"
    echo ""
    echo -e "${YELLOW}Use 'docker-compose logs -f' to view logs${NC}"
    echo -e "${YELLOW}Use './start-trustvault.sh stop' to stop all services${NC}"
}

# Main script logic
case "${1:-start}" in
    "stop")
        stop_services
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    "clean")
        clean_services
        ;;
    "build")
        start_services "true"
        ;;
    "start"|"")
        start_services "false"
        ;;
    *)
        echo "Usage: $0 {start|stop|status|logs|clean|build}"
        echo "  start  - Start TrustVault services"
        echo "  stop   - Stop all services"
        echo "  status - Show service status"
        echo "  logs   - Show service logs"
        echo "  clean  - Clean up all containers and volumes"
        echo "  build  - Build images and start services"
        exit 1
        ;;
esac
