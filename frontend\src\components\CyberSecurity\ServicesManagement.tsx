import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  Snackbar,
  Tooltip,
  CircularProgress
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Visibility,
  PlayArrow,
  Stop,
  Refresh,
  Settings,
  TrendingUp
} from '@mui/icons-material';
import api from '../../services/api';

interface CyberSecurityService {
  id: number;
  name: string;
  service_type: string;
  description: string;
  status: string;
  health_status: string;
  endpoint_url: string;
  uptime_percentage: number;
  response_time_ms: number;
  last_health_check: string;
  is_monitoring_enabled: boolean;
  created_at: string;
  updated_at: string;
  latest_metrics?: {
    events_processed: number;
    threats_detected: number;
    threats_blocked: number;
    error_rate: number;
    timestamp: string;
  };
}

const SERVICE_TYPES = [
  { value: 'IDS', label: 'Intrusion Detection System' },
  { value: 'IPS', label: 'Intrusion Prevention System' },
  { value: 'SIEM', label: 'Security Information and Event Management' },
  { value: 'MONITORING', label: 'Security Monitoring' },
  { value: 'ALERTING', label: 'Alert Management' },
  { value: 'WAF', label: 'Web Application Firewall' },
  { value: 'ANTIVIRUS', label: 'Antivirus Protection' },
  { value: 'VULNERABILITY_SCANNER', label: 'Vulnerability Scanner' },
  { value: 'THREAT_INTELLIGENCE', label: 'Threat Intelligence' },
  { value: 'BACKUP', label: 'Backup System' }
];

const ServicesManagement: React.FC = () => {
  const [services, setServices] = useState<CyberSecurityService[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingService, setEditingService] = useState<CyberSecurityService | null>(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    service_type: '',
    description: '',
    endpoint_url: '',
    is_monitoring_enabled: true
  });

  useEffect(() => {
    loadServices();
  }, []);

  const loadServices = async () => {
    try {
      const response = await api.get('/security/cybersecurity/services/');
      setServices(response.data);
    } catch (error) {
      console.error('Failed to load services:', error);
      showSnackbar('Failed to load services', 'error');
    } finally {
      setLoading(false);
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCreateService = () => {
    setEditingService(null);
    setFormData({
      name: '',
      service_type: '',
      description: '',
      endpoint_url: '',
      is_monitoring_enabled: true
    });
    setDialogOpen(true);
  };

  const handleEditService = (service: CyberSecurityService) => {
    setEditingService(service);
    setFormData({
      name: service.name,
      service_type: service.service_type,
      description: service.description,
      endpoint_url: service.endpoint_url,
      is_monitoring_enabled: service.is_monitoring_enabled
    });
    setDialogOpen(true);
  };

  const handleSaveService = async () => {
    try {
      if (editingService) {
        // Update existing service
        await api.put(`/security/cybersecurity/services/${editingService.id}/`, formData);
        showSnackbar('Service updated successfully', 'success');
      } else {
        // Create new service
        await api.post('/security/cybersecurity/services/', formData);
        showSnackbar('Service created successfully', 'success');
      }
      
      setDialogOpen(false);
      loadServices();
    } catch (error) {
      console.error('Failed to save service:', error);
      showSnackbar('Failed to save service', 'error');
    }
  };

  const handleDeleteService = async (serviceId: number) => {
    if (window.confirm('Are you sure you want to delete this service?')) {
      try {
        await api.delete(`/security/cybersecurity/services/${serviceId}/`);
        showSnackbar('Service deleted successfully', 'success');
        loadServices();
      } catch (error) {
        console.error('Failed to delete service:', error);
        showSnackbar('Failed to delete service', 'error');
      }
    }
  };

  const handleToggleService = async (service: CyberSecurityService) => {
    try {
      const newStatus = service.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
      await api.put(`/security/cybersecurity/services/${service.id}/`, {
        status: newStatus
      });
      showSnackbar(`Service ${newStatus.toLowerCase()} successfully`, 'success');
      loadServices();
    } catch (error) {
      console.error('Failed to toggle service:', error);
      showSnackbar('Failed to toggle service', 'error');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'success';
      case 'INACTIVE': return 'error';
      case 'MAINTENANCE': return 'warning';
      case 'ERROR': return 'error';
      case 'INITIALIZING': return 'info';
      default: return 'default';
    }
  };

  const getHealthStatusColor = (healthStatus: string) => {
    switch (healthStatus) {
      case 'HEALTHY': return 'success';
      case 'WARNING': return 'warning';
      case 'CRITICAL': return 'error';
      case 'UNKNOWN': return 'default';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" component="h1">
          Cybersecurity Services Management
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadServices}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreateService}
          >
            Add Service
          </Button>
        </Box>
      </Box>

      {/* Services Table */}
      <Card>
        <CardContent>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Service Name</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Health</TableCell>
                  <TableCell>Uptime</TableCell>
                  <TableCell>Response Time</TableCell>
                  <TableCell>Monitoring</TableCell>
                  <TableCell>Metrics</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {services.map((service) => (
                  <TableRow key={service.id}>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {service.name}
                        </Typography>
                        {service.description && (
                          <Typography variant="caption" color="textSecondary">
                            {service.description}
                          </Typography>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={service.service_type}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={service.status}
                        size="small"
                        color={getStatusColor(service.status) as any}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={service.health_status}
                        size="small"
                        color={getHealthStatusColor(service.health_status) as any}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {service.uptime_percentage.toFixed(1)}%
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {service.response_time_ms}ms
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={service.is_monitoring_enabled ? 'Enabled' : 'Disabled'}
                        size="small"
                        color={service.is_monitoring_enabled ? 'success' : 'default'}
                      />
                    </TableCell>
                    <TableCell>
                      {service.latest_metrics && (
                        <Box>
                          <Typography variant="caption" display="block">
                            Events: {service.latest_metrics.events_processed}
                          </Typography>
                          <Typography variant="caption" display="block">
                            Threats: {service.latest_metrics.threats_detected}
                          </Typography>
                          <Typography variant="caption" display="block">
                            Blocked: {service.latest_metrics.threats_blocked}
                          </Typography>
                        </Box>
                      )}
                    </TableCell>
                    <TableCell>
                      <Box display="flex" gap={0.5}>
                        <Tooltip title={service.status === 'ACTIVE' ? 'Stop Service' : 'Start Service'}>
                          <IconButton
                            size="small"
                            onClick={() => handleToggleService(service)}
                            color={service.status === 'ACTIVE' ? 'error' : 'success'}
                          >
                            {service.status === 'ACTIVE' ? <Stop /> : <PlayArrow />}
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit Service">
                          <IconButton
                            size="small"
                            onClick={() => handleEditService(service)}
                          >
                            <Edit />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            onClick={() => {/* Navigate to service details */}}
                          >
                            <Visibility />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete Service">
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteService(service.id)}
                            color="error"
                          >
                            <Delete />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Service Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingService ? 'Edit Service' : 'Create New Service'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Service Name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Service Type</InputLabel>
                <Select
                  value={formData.service_type}
                  onChange={(e) => setFormData({ ...formData, service_type: e.target.value })}
                  label="Service Type"
                >
                  {SERVICE_TYPES.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Endpoint URL"
                value={formData.endpoint_url}
                onChange={(e) => setFormData({ ...formData, endpoint_url: e.target.value })}
                placeholder="http://localhost:9090"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_monitoring_enabled}
                    onChange={(e) => setFormData({ ...formData, is_monitoring_enabled: e.target.checked })}
                  />
                }
                label="Enable Monitoring"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveService} variant="contained">
            {editingService ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ServicesManagement;
