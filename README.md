# 🛡️ TrustVault - Comprehensive Security Platform

[![Security Rating](https://img.shields.io/badge/Security-A+-green.svg)](https://github.com/Nidhaldhawi/TrustVault)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://docker.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

TrustVault is a comprehensive cybersecurity platform that combines portfolio management with advanced security monitoring, threat detection, and compliance features. Built with enterprise-grade security in mind.

## 🚀 Quick Start

### Prerequisites

- **Docker** (v20.10+) and **Docker Compose** (v2.0+)
- **Git** for cloning the repository
- **4GB+ RAM** recommended for all services
- **10GB+ disk space** for logs and data

### 1. Clone the Repository

```bash
git clone https://github.com/Nidhaldhawi/TrustVault.git
cd TrustVault
```

### 2. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit the .env file with your secure values
# IMPORTANT: Change all default passwords and keys!
```

### 3. Create Required Directories and Secrets

```bash
# Create logs directory
mkdir -p logs/{nginx,django,celery,suricata,modsecurity}

# Create secrets directory
mkdir -p secrets

# Generate secure passwords (Linux/macOS)
openssl rand -base64 32 > secrets/django_secret.txt
openssl rand -base64 32 > secrets/db_password.txt

# For Windows PowerShell:
# [System.Web.Security.Membership]::GeneratePassword(32, 8) | Out-File -Encoding ASCII secrets/django_secret.txt
# [System.Web.Security.Membership]::GeneratePassword(32, 8) | Out-File -Encoding ASCII secrets/db_password.txt
```

### 4. Start TrustVault

```bash
# Make startup script executable (Linux/macOS)
chmod +x start-trustvault.sh

# Start all services
./start-trustvault.sh

# For Windows, use PowerShell script:
# .\start-trustvault.ps1
```

## 🌐 Access Points

Once all services are running, access TrustVault through:

| Service | URL | Default Credentials |
|---------|-----|-------------------|
| **Main Application** | http://localhost | Register new account |
| **API Documentation** | http://localhost/api/docs/ | - |
| **Wazuh SIEM** | http://localhost:5601 | admin / admin |
| **Grafana Monitoring** | http://localhost:3001 | admin / (from .env) |
| **Prometheus Metrics** | http://localhost:9090 | - |
| **AlertManager** | http://localhost:9093 | - |
| **Vault Secrets** | http://localhost:8200 | (from .env) |

## 🏗️ Architecture Overview

TrustVault consists of multiple security layers:

### Application Layer
- **Django Backend**: RESTful API with advanced security features
- **React Frontend**: Modern, responsive security dashboard
- **Celery Workers**: Background task processing

### Security Layer
- **Nginx + ModSecurity**: Web Application Firewall (WAF)
- **Fail2Ban**: DDoS protection and intrusion prevention
- **Suricata**: Network Intrusion Detection System (IDS/IPS)
- **Wazuh**: Security Information and Event Management (SIEM)

### Data Layer
- **PostgreSQL**: Encrypted primary database
- **Redis**: Session and cache storage
- **HashiCorp Vault**: Secrets management

### Monitoring Layer
- **Prometheus**: Metrics collection
- **Grafana**: Visualization and dashboards
- **AlertManager**: Alert routing and notifications

## 🔧 Configuration

### Environment Variables (.env)

Key variables you must configure:

```bash
# Database Security
DB_PASSWORD=your_super_secure_db_password_here
REDIS_PASSWORD=your_redis_password_here

# Application Security
DJANGO_SECRET_KEY=your_django_secret_key_here
JWT_SECRET_KEY=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here

# Vault Configuration
VAULT_ROOT_TOKEN=your_vault_root_token_here

# Monitoring
GRAFANA_USER=admin
GRAFANA_PASSWORD=your_grafana_password_here

# Email Alerts
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password_here
```

### SSL/TLS Setup (Production)

```bash
# Generate SSL certificates
mkdir -p nginx/ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout nginx/ssl/trustvault.key \
  -out nginx/ssl/trustvault.crt \
  -subj "/C=US/ST=State/L=City/O=Organization/CN=trustvault.local"
```

## 🛠️ Management Commands

### Service Management

```bash
# Start services
./start-trustvault.sh start

# Stop all services
./start-trustvault.sh stop

# View service status
./start-trustvault.sh status

# View logs
./start-trustvault.sh logs

# Rebuild and start
./start-trustvault.sh build

# Clean up everything
./start-trustvault.sh clean
```

### Database Management

```bash
# Run Django migrations
docker-compose exec django python manage.py migrate

# Create superuser
docker-compose exec django python manage.py createsuperuser

# Initialize cybersecurity services
docker-compose exec django python manage.py init_cybersecurity_services

# Collect static files
docker-compose exec django python manage.py collectstatic --noinput
```

### Security Testing

```bash
# Run security tests
./scripts/powerful-security-test.sh

# Advanced attack simulation
./scripts/advanced-attack-simulation.sh

# Real-time monitoring check
./scripts/real-time-monitoring-check.sh
```

## 🔍 Monitoring & Alerts

### Grafana Dashboards

Access Grafana at http://localhost:3001 to view:
- **TrustVault Overview**: System health and performance
- **Security Dashboard**: Threat detection and incidents
- **Infrastructure Metrics**: Container and host monitoring

### Wazuh SIEM

Access Wazuh at http://localhost:5601 for:
- Security event analysis
- Compliance reporting
- Threat intelligence
- Incident response

### Prometheus Metrics

Key metrics available at http://localhost:9090:
- Application performance
- Security events
- Infrastructure health
- Custom business metrics

## 🚨 Troubleshooting

### Common Issues

1. **Services won't start**
   ```bash
   # Check Docker daemon
   docker info
   
   # Check logs
   docker-compose logs
   ```

2. **Database connection errors**
   ```bash
   # Reset database
   docker-compose down -v
   docker-compose up -d postgres
   ```

3. **Permission issues**
   ```bash
   # Fix log permissions
   sudo chown -R $USER:$USER logs/
   chmod -R 755 logs/
   ```

4. **Memory issues**
   ```bash
   # Check resource usage
   docker stats
   
   # Increase Docker memory limit to 4GB+
   ```

### Health Checks

```bash
# Check all services
curl http://localhost/health/

# Check API status
curl http://localhost/api/v1/health/

# Check database
docker-compose exec postgres pg_isready
```

## 📊 Performance Optimization

### Production Deployment

1. **Resource Allocation**
   - Minimum 4GB RAM
   - 4+ CPU cores recommended
   - SSD storage for databases

2. **Security Hardening**
   - Change all default passwords
   - Enable SSL/TLS certificates
   - Configure firewall rules
   - Set up log rotation

3. **Monitoring Setup**
   - Configure alert thresholds
   - Set up email notifications
   - Enable backup strategies





## 🔐 Security Features

### Multi-Factor Authentication (MFA)
- TOTP (Time-based One-Time Password)
- Hardware token support



### Encryption & Data Protection
- AES-256-GCM encryption at rest
- TLS 1.3 for data in transit
- End-to-end encryption for sensitive data
- Secure key management with HashiCorp Vault

## 📋 API Documentation

### Authentication Endpoints
```bash
POST /api/v1/auth/login/          # User login
POST /api/v1/auth/register/       # User registration
POST /api/v1/auth/refresh/        # Token refresh
POST /api/v1/auth/logout/         # User logout
POST /api/v1/auth/mfa/setup/      # MFA setup
POST /api/v1/auth/mfa/verify/     # MFA verification
```

### Security Endpoints
```bash
GET  /api/v1/security/threats/    # List threats
GET  /api/v1/security/incidents/  # Security incidents
POST /api/v1/security/scan/       # Trigger security scan
GET  /api/v1/security/compliance/ # Compliance status
```

### Portfolio Endpoints
```bash
GET  /api/v1/portfolios/          # List portfolios
POST /api/v1/portfolios/          # Create portfolio
GET  /api/v1/portfolios/{id}/     # Portfolio details
PUT  /api/v1/portfolios/{id}/     # Update portfolio
DELETE /api/v1/portfolios/{id}/   # Delete portfolio
```

## 🧪 Testing

### Running Tests

```bash
# Backend tests
docker-compose exec django python manage.py test

# Frontend tests
docker-compose exec react npm test

# Security tests
./scripts/security-test.sh

# Load testing
./scripts/load-test.sh
```

### Test Coverage

```bash
# Generate coverage report
docker-compose exec django coverage run --source='.' manage.py test
docker-compose exec django coverage report
docker-compose exec django coverage html
```

## 🔄 Backup & Recovery

### Database Backup

```bash
# Create backup
docker-compose exec postgres pg_dump -U trustvault trustvault > backup.sql

# Restore backup
docker-compose exec -T postgres psql -U trustvault trustvault < backup.sql
```

### Full System Backup

```bash
# Backup all volumes
docker run --rm -v trustvault_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz /data

# Backup configuration
tar czf config_backup.tar.gz .env docker-compose.yml nginx/ wazuh/ grafana/
```

