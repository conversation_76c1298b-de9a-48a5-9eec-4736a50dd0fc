# TrustVault - Alert Processing Tasks

import logging
from decimal import Decimal
from typing import List, Dict, Any
from django.utils import timezone
from django.db import transaction
from celery import shared_task
from celery.exceptions import Retry

from .models import PriceAlert, AlertHistory, AlertStatus, NotificationPreference
from .services import NotificationService
from apps.portfolio.models import Asset, Portfolio
from apps.portfolio.services import PriceService

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def process_price_alerts(self):
    """Process all active price alerts."""
    try:
        logger.info("Starting price alert processing")
        
        # Get all active alerts
        active_alerts = PriceAlert.objects.filter(
            status=AlertStatus.ACTIVE
        ).select_related('user', 'asset', 'portfolio')
        
        processed_count = 0
        triggered_count = 0
        
        for alert in active_alerts:
            try:
                if process_single_alert(alert.id):
                    triggered_count += 1
                processed_count += 1
            except Exception as e:
                logger.error(f"Error processing alert {alert.id}: {str(e)}")
                continue
        
        logger.info(f"Processed {processed_count} alerts, {triggered_count} triggered")
        return {
            'processed': processed_count,
            'triggered': triggered_count,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"Error in process_price_alerts: {str(exc)}")
        raise self.retry(exc=exc)


@shared_task(bind=True, max_retries=3, default_retry_delay=30)
def process_single_alert(self, alert_id: int) -> bool:
    """Process a single price alert."""
    try:
        alert = PriceAlert.objects.select_related(
            'user', 'asset', 'portfolio'
        ).get(id=alert_id)
        
        # Check if alert can trigger
        if not alert.can_trigger():
            return False
        
        # Get current value based on alert type
        current_value = None
        
        if alert.asset:
            # Asset-based alert
            current_value = get_asset_current_value(alert.asset, alert.alert_type)
        elif alert.portfolio:
            # Portfolio-based alert
            current_value = get_portfolio_current_value(alert.portfolio, alert.alert_type)
        
        if current_value is None:
            logger.warning(f"Could not get current value for alert {alert_id}")
            return False
        
        # Check if threshold is met
        threshold_met = check_threshold(
            current_value, 
            alert.threshold_value, 
            alert.comparison_operator
        )
        
        if threshold_met:
            # Trigger the alert
            with transaction.atomic():
                alert.trigger()
                
                # Create history record
                history = AlertHistory.objects.create(
                    alert=alert,
                    user=alert.user,
                    triggered_value=current_value,
                    threshold_value=alert.threshold_value,
                    message=generate_alert_message(alert, current_value)
                )
                
                # Send notifications
                notification_service = NotificationService()
                results = notification_service.send_alert_notification(alert, float(current_value))
                
                # Update history with notification results
                history.notifications_sent = [
                    channel for channel, success in results.items() if success
                ]
                history.notification_failures = [
                    channel for channel, success in results.items() if not success
                ]
                history.save()
                
                logger.info(f"Alert {alert_id} triggered successfully")
                return True
        
        # Update last checked time
        alert.last_checked_at = timezone.now()
        alert.save(update_fields=['last_checked_at'])
        
        return False
        
    except PriceAlert.DoesNotExist:
        logger.error(f"Alert {alert_id} not found")
        return False
    except Exception as exc:
        logger.error(f"Error processing alert {alert_id}: {str(exc)}")
        raise self.retry(exc=exc)


def get_asset_current_value(asset: Asset, alert_type: str) -> Decimal:
    """Get current value for asset-based alerts."""
    price_service = PriceService()
    
    try:
        if alert_type in ['PRICE_ABOVE', 'PRICE_BELOW']:
            # Get current price
            price_data = price_service.get_current_price(asset.symbol)
            return Decimal(str(price_data.get('price', 0)))
            
        elif alert_type == 'PRICE_CHANGE':
            # Get price change percentage
            price_data = price_service.get_current_price(asset.symbol)
            return Decimal(str(price_data.get('change_percent', 0)))
            
        elif alert_type == 'VOLUME_SPIKE':
            # Get volume data
            price_data = price_service.get_current_price(asset.symbol)
            return Decimal(str(price_data.get('volume', 0)))
            
    except Exception as e:
        logger.error(f"Error getting asset value for {asset.symbol}: {str(e)}")
        return None
    
    return None


def get_portfolio_current_value(portfolio: Portfolio, alert_type: str) -> Decimal:
    """Get current value for portfolio-based alerts."""
    try:
        if alert_type == 'PORTFOLIO_VALUE':
            # Get total portfolio value
            return portfolio.get_total_value()
            
        elif alert_type == 'PORTFOLIO_CHANGE':
            # Get portfolio change percentage
            return portfolio.get_change_percentage()
            
    except Exception as e:
        logger.error(f"Error getting portfolio value for {portfolio.id}: {str(e)}")
        return None
    
    return None


def check_threshold(current_value: Decimal, threshold_value: Decimal, operator: str) -> bool:
    """Check if threshold condition is met."""
    if operator == 'GT':
        return current_value > threshold_value
    elif operator == 'LT':
        return current_value < threshold_value
    elif operator == 'GTE':
        return current_value >= threshold_value
    elif operator == 'LTE':
        return current_value <= threshold_value
    
    return False


def generate_alert_message(alert: PriceAlert, current_value: Decimal) -> str:
    """Generate alert message."""
    target = alert.asset.symbol if alert.asset else alert.portfolio.name
    
    if alert.alert_type == 'PRICE_ABOVE':
        return f"{target} price ${current_value} is above your threshold of ${alert.threshold_value}"
    elif alert.alert_type == 'PRICE_BELOW':
        return f"{target} price ${current_value} is below your threshold of ${alert.threshold_value}"
    elif alert.alert_type == 'PRICE_CHANGE':
        return f"{target} price changed {current_value}% (threshold: {alert.threshold_value}%)"
    elif alert.alert_type == 'VOLUME_SPIKE':
        return f"{target} volume spike detected: {current_value} (threshold: {alert.threshold_value})"
    elif alert.alert_type == 'PORTFOLIO_VALUE':
        return f"Portfolio value ${current_value} triggered alert (threshold: ${alert.threshold_value})"
    elif alert.alert_type == 'PORTFOLIO_CHANGE':
        return f"Portfolio changed {current_value}% (threshold: {alert.threshold_value}%)"
    
    return f"Alert triggered for {target}: {current_value} vs {alert.threshold_value}"


@shared_task
def cleanup_expired_alerts():
    """Clean up expired alerts."""
    try:
        expired_count = PriceAlert.objects.filter(
            expires_at__lt=timezone.now(),
            status=AlertStatus.ACTIVE
        ).update(status=AlertStatus.EXPIRED)
        
        logger.info(f"Marked {expired_count} alerts as expired")
        return expired_count
        
    except Exception as e:
        logger.error(f"Error cleaning up expired alerts: {str(e)}")
        return 0


@shared_task
def send_daily_alert_summary():
    """Send daily summary of alert activity."""
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        # Get users with notification preferences
        users_with_prefs = User.objects.filter(
            notification_preferences__isnull=False
        ).select_related('notification_preferences')
        
        for user in users_with_prefs:
            if user.notification_preferences.email_enabled:
                # Generate summary for user
                summary_data = generate_user_alert_summary(user)
                
                # Send summary email
                notification_service = NotificationService()
                notification_service.send_daily_summary(user, summary_data)
        
        logger.info("Daily alert summaries sent")
        
    except Exception as e:
        logger.error(f"Error sending daily summaries: {str(e)}")


def generate_user_alert_summary(user) -> Dict[str, Any]:
    """Generate alert summary for a user."""
    yesterday = timezone.now() - timezone.timedelta(days=1)
    
    return {
        'active_alerts': user.price_alerts.filter(status=AlertStatus.ACTIVE).count(),
        'triggered_today': user.alert_history.filter(created_at__gte=yesterday).count(),
        'total_notifications': user.notifications.filter(created_at__gte=yesterday).count(),
        'failed_notifications': user.notifications.filter(
            created_at__gte=yesterday,
            status='FAILED'
        ).count(),
    }


@shared_task
def update_alert_metrics():
    """Update Prometheus metrics for alerts."""
    try:
        from .metrics import update_active_alerts_gauge
        
        # Update active alerts count
        active_count = PriceAlert.objects.filter(status=AlertStatus.ACTIVE).count()
        update_active_alerts_gauge(active_count)
        
        logger.info(f"Updated alert metrics: {active_count} active alerts")
        
    except Exception as e:
        logger.error(f"Error updating alert metrics: {str(e)}")
