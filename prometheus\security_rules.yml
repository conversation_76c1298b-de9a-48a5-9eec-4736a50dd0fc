# Security alerting rules for TrustVault
groups:
  - name: security.rules
    rules:
      # High CPU usage - potential DoS attack
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "High CPU usage detected on {{ $labels.instance }}"
          description: "CPU usage is above 80% for more than 5 minutes on {{ $labels.instance }}"

      # High memory usage
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage is above 85% on {{ $labels.instance }}"

      # Disk space running low
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
        for: 5m
        labels:
          severity: critical
          category: security
        annotations:
          summary: "Disk space low on {{ $labels.instance }}"
          description: "Disk space is below 10% on {{ $labels.instance }}"

      # Too many failed login attempts
      - alert: HighFailedLogins
        expr: increase(django_failed_login_attempts_total[5m]) > 10
        for: 1m
        labels:
          severity: critical
          category: security
        annotations:
          summary: "High number of failed login attempts"
          description: "More than 10 failed login attempts in the last 5 minutes"

      # Database connection issues
      - alert: DatabaseConnectionFailure
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
          category: security
        annotations:
          summary: "Database connection failure"
          description: "PostgreSQL database is not responding"

      # Redis connection issues
      - alert: RedisConnectionFailure
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          category: security
        annotations:
          summary: "Redis connection failure"
          description: "Redis cache is not responding"

      # Application down
      - alert: ApplicationDown
        expr: up{job="django"} == 0
        for: 1m
        labels:
          severity: critical
          category: security
        annotations:
          summary: "TrustVault application is down"
          description: "Django application is not responding"

      # High HTTP error rate
      - alert: HighHTTPErrorRate
        expr: rate(nginx_http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "High HTTP 5xx error rate"
          description: "HTTP 5xx error rate is above 10% for 5 minutes"

      # Suspicious network activity
      - alert: HighNetworkTraffic
        expr: rate(node_network_receive_bytes_total[5m]) > 100000000
        for: 5m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "High network traffic detected"
          description: "Network receive rate is above 100MB/s on {{ $labels.instance }}"

      # Container restart frequency
      - alert: ContainerRestartingFrequently
        expr: increase(container_start_time_seconds[1h]) > 5
        for: 5m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "Container restarting frequently"
          description: "Container {{ $labels.name }} has restarted more than 5 times in the last hour"

      # SSL certificate expiry
      - alert: SSLCertificateExpiringSoon
        expr: probe_ssl_earliest_cert_expiry - time() < 86400 * 30
        for: 1h
        labels:
          severity: warning
          category: security
        annotations:
          summary: "SSL certificate expiring soon"
          description: "SSL certificate for {{ $labels.instance }} expires in less than 30 days"

      # ModSecurity blocks
      - alert: HighModSecurityBlocks
        expr: increase(modsecurity_blocked_requests_total[5m]) > 50
        for: 2m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "High number of ModSecurity blocks"
          description: "More than 50 requests blocked by ModSecurity in 5 minutes"

      # Vault seal status
      - alert: VaultSealed
        expr: vault_core_unsealed == 0
        for: 1m
        labels:
          severity: critical
          category: security
        annotations:
          summary: "HashiCorp Vault is sealed"
          description: "Vault is in sealed state and cannot serve requests"

      # Wazuh agent disconnected
      - alert: WazuhAgentDisconnected
        expr: wazuh_agent_status == 0
        for: 5m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "Wazuh agent disconnected"
          description: "Wazuh security agent is disconnected from manager"

      # Anomalous user behavior
      - alert: AnomalousUserBehavior
        expr: increase(django_user_login_count[1h]) > 100
        for: 5m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "Anomalous user login behavior"
          description: "User has logged in more than 100 times in the last hour"
