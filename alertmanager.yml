global:
  resolve_timeout: 5m

# Define routing tree
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'trustvault-alerts'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 5s
      repeat_interval: 30m
    - match:
        severity: warning
      receiver: 'warning-alerts'
      repeat_interval: 2h
    - match:
        alertname: 'InstanceDown'
      receiver: 'instance-down-alerts'
      group_wait: 5s
      repeat_interval: 15m

# Define receivers
receivers:
  - name: 'trustvault-alerts'
    webhook_configs:
      - url: 'http://localhost:9093/api/v1/alerts'
        send_resolved: true

  - name: 'critical-alerts'
    webhook_configs:
      - url: 'http://localhost:9093/api/v1/alerts'
        send_resolved: true

  - name: 'warning-alerts'
    webhook_configs:
      - url: 'http://localhost:9093/api/v1/alerts'
        send_resolved: true

  - name: 'instance-down-alerts'
    webhook_configs:
      - url: 'http://localhost:9093/api/v1/alerts'
        send_resolved: true

# Inhibit rules
inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'cluster', 'service']
