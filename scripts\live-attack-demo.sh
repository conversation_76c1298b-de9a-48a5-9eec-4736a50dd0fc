#!/bin/bash
# TrustVault Live Attack Simulation for Client Demo
# This script demonstrates security features in a controlled environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Demo configuration
TARGET_URL="http://localhost"
DEMO_DELAY=3

log_demo() {
    echo -e "${CYAN}[DEMO]${NC} $1"
}

log_attack() {
    echo -e "${RED}[ATTACK]${NC} $1"
}

log_defense() {
    echo -e "${GREEN}[DEFENSE]${NC} $1"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

wait_for_demo() {
    echo -e "${YELLOW}Press Enter to continue to next demonstration...${NC}"
    read -r
}

show_header() {
    clear
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    TrustVault Security Demo                  ║"
    echo "║              Live Attack Simulation & Response              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
}

# Demo 1: Security Headers Validation
demo_security_headers() {
    log_demo "Demonstration 1: Security Headers Protection"
    echo "=============================================="
    echo ""

    log_info "Testing security headers implementation..."
    echo ""

    log_attack "Simulating request to check security headers..."
    echo "Command: curl -I $TARGET_URL/health"
    echo ""

    HEADERS=$(curl -s -I $TARGET_URL/health 2>/dev/null)

    echo "Response Headers:"
    echo "$HEADERS" | grep -E "(X-Frame-Options|X-Content-Type-Options|Strict-Transport-Security|Content-Security-Policy)" || echo "No security headers found"
    echo ""

    # Check each header
    if echo "$HEADERS" | grep -q "X-Frame-Options"; then
        log_defense "✅ X-Frame-Options: Clickjacking protection active"
    else
        log_attack "❌ X-Frame-Options: Missing clickjacking protection"
    fi

    if echo "$HEADERS" | grep -q "X-Content-Type-Options"; then
        log_defense "✅ X-Content-Type-Options: MIME sniffing protection active"
    else
        log_attack "❌ X-Content-Type-Options: Missing MIME protection"
    fi

    if echo "$HEADERS" | grep -q "Strict-Transport-Security"; then
        log_defense "✅ HSTS: HTTPS enforcement active"
    else
        log_attack "❌ HSTS: Missing HTTPS enforcement"
    fi

    if echo "$HEADERS" | grep -q "Content-Security-Policy"; then
        log_defense "✅ CSP: Content injection protection active"
    else
        log_attack "❌ CSP: Missing content injection protection"
    fi

    echo ""
    log_info "Security headers provide the first line of defense against common web attacks"
    wait_for_demo
}

# Demo 2: Rate Limiting Test
demo_rate_limiting() {
    log_demo "Demonstration 2: Rate Limiting & DDoS Protection"
    echo "================================================="
    echo ""

    log_info "Testing rate limiting with rapid requests..."
    echo ""

    log_attack "Simulating rapid-fire requests (potential DDoS)..."
    echo "Command: Multiple concurrent requests to /health endpoint"
    echo ""

    # Send multiple requests rapidly
    for i in {1..15}; do
        RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $TARGET_URL/health 2>/dev/null)
        echo "Request $i: HTTP $RESPONSE"
        if [ "$RESPONSE" = "429" ] || [ "$RESPONSE" = "503" ]; then
            log_defense "✅ Rate limiting activated - Request blocked!"
            break
        fi
        sleep 0.1
    done

    echo ""
    log_info "Rate limiting prevents service overload and DDoS attacks"
    wait_for_demo
}

# Demo 3: SQL Injection Attempt
demo_sql_injection() {
    log_demo "Demonstration 3: SQL Injection Attack Detection"
    echo "==============================================="
    echo ""

    log_info "Testing SQL injection protection..."
    echo ""

    log_attack "Attempting SQL injection attack..."
    PAYLOAD="1' OR '1'='1"
    echo "Malicious payload: $PAYLOAD"
    echo "Command: curl \"$TARGET_URL/api/test?id=$PAYLOAD\""
    echo ""

    RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$TARGET_URL/api/test?id=$PAYLOAD" 2>/dev/null)

    if [ "$RESPONSE" = "403" ] || [ "$RESPONSE" = "406" ]; then
        log_defense "✅ SQL Injection BLOCKED - HTTP $RESPONSE"
        log_defense "✅ WAF detected malicious SQL pattern"
    else
        log_attack "⚠️  SQL Injection not blocked - HTTP $RESPONSE"
        log_info "Note: WAF may need additional configuration"
    fi

    echo ""
    log_info "SQL injection is one of the most common web application attacks"
    wait_for_demo
}

# Demo 4: XSS Attack Prevention
demo_xss_protection() {
    log_demo "Demonstration 4: Cross-Site Scripting (XSS) Prevention"
    echo "======================================================="
    echo ""

    log_info "Testing XSS attack prevention..."
    echo ""

    log_attack "Attempting XSS attack..."
    PAYLOAD="<script>alert('xss')</script>"
    echo "Malicious payload: $PAYLOAD"
    echo "Command: curl \"$TARGET_URL/api/search?q=$PAYLOAD\""
    echo ""

    RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$TARGET_URL/api/search?q=$PAYLOAD" 2>/dev/null)

    if [ "$RESPONSE" = "403" ] || [ "$RESPONSE" = "406" ]; then
        log_defense "✅ XSS Attack BLOCKED - HTTP $RESPONSE"
        log_defense "✅ Security headers prevented script execution"
    else
        log_attack "⚠️  XSS Attack not blocked - HTTP $RESPONSE"
        log_info "Note: XSS protection relies on security headers and WAF rules"
    fi

    echo ""
    log_info "XSS attacks can steal user data and hijack sessions"
    wait_for_demo
}

# Demo 5: Path Traversal Attack
demo_path_traversal() {
    log_demo "Demonstration 5: Path Traversal Attack Detection"
    echo "================================================"
    echo ""

    log_info "Testing path traversal protection..."
    echo ""

    log_attack "Attempting path traversal attack..."
    PAYLOAD="../../../etc/passwd"
    echo "Malicious payload: $PAYLOAD"
    echo "Command: curl \"$TARGET_URL/$PAYLOAD\""
    echo ""

    RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$TARGET_URL/$PAYLOAD" 2>/dev/null)

    if [ "$RESPONSE" = "403" ] || [ "$RESPONSE" = "404" ]; then
        log_defense "✅ Path Traversal BLOCKED - HTTP $RESPONSE"
        log_defense "✅ Server configuration prevents directory traversal"
    else
        log_attack "⚠️  Path Traversal not blocked - HTTP $RESPONSE"
        log_info "Note: Path traversal protection depends on server configuration"
    fi

    echo ""
    log_info "Path traversal attacks attempt to access sensitive system files"
    wait_for_demo
}

# Demo 6: Service Health Check
demo_service_health() {
    log_demo "Demonstration 6: Security Service Health Monitoring"
    echo "=================================================="
    echo ""

    log_info "Checking security service status..."
    echo ""

    # Check Docker services
    log_info "Security Services Status:"
    echo ""

    services=("trustvault-nginx" "trustvault-waf" "trustvault-suricata" "trustvault-fail2ban" "trustvault-wazuh-manager")

    for service in "${services[@]}"; do
        if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$service.*Up"; then
            log_defense "✅ $service: Running"
        else
            log_attack "❌ $service: Not running or unhealthy"
        fi
    done

    echo ""
    log_info "All security services are monitored 24/7 for availability"
    wait_for_demo
}

# Main demo execution
main() {
    show_header

    log_info "Welcome to the TrustVault Security Demonstration!"
    log_info "This demo will show live security features protecting your portfolio application."
    echo ""
    log_info "We will demonstrate:"
    echo "  1. Security Headers Protection"
    echo "  2. Rate Limiting & DDoS Protection"
    echo "  3. SQL Injection Detection"
    echo "  4. XSS Attack Prevention"
    echo "  5. Path Traversal Protection"
    echo "  6. Security Service Monitoring"
    echo ""
    wait_for_demo

    # Run demonstrations
    demo_security_headers
    demo_rate_limiting
    demo_sql_injection
    demo_xss_protection
    demo_path_traversal
    demo_service_health

    # Summary
    show_header
    log_demo "Security Demonstration Complete!"
    echo "================================"
    echo ""
    log_defense "✅ Multi-layer security architecture demonstrated"
    log_defense "✅ Real-time threat detection and response"
    log_defense "✅ Enterprise-grade protection capabilities"
    log_defense "✅ 24/7 monitoring and alerting"
    echo ""
    log_info "TrustVault provides comprehensive security for financial applications"
    log_info "All security events are logged and can be viewed in:"
    echo "  • Grafana Dashboard: http://localhost:3001"
    echo "  • Wazuh SIEM: http://localhost:5601"
    echo "  • Prometheus Metrics: http://localhost:9090"
    echo ""
    log_info "Thank you for the demonstration!"
}

# Run the demo
main "$@"