#!/bin/bash

# TrustVault - Comprehensive Security Testing Script
# Tests all cybersecurity services and their functionality

echo "🛡️ TrustVault - Comprehensive Security Testing"
echo "=============================================="

# Function to test service status
test_service() {
    local service=$1
    local container=$2
    
    echo -n "Testing $service... "
    
    if docker-compose ps $container | grep -q "Up"; then
        echo "✅ Running"
        return 0
    else
        echo "❌ Not running"
        return 1
    fi
}

# Function to test network connectivity
test_connectivity() {
    local service=$1
    local host=$2
    local port=$3
    
    echo -n "Testing $service connectivity ($host:$port)... "
    
    if timeout 5 bash -c "</dev/tcp/$host/$port" 2>/dev/null; then
        echo "✅ Connected"
        return 0
    else
        echo "❌ Connection failed"
        return 1
    fi
}

echo "🔍 Testing Core Security Services"
echo "================================="

# Test ModSecurity WAF
test_service "ModSecurity WAF" "modsecurity"
if [ $? -eq 0 ]; then
    echo "  📊 WAF Rules: $(docker logs trustvault-waf 2>/dev/null | grep -o '[0-9]* rules loaded' | tail -1 || echo 'Unknown')"
    test_connectivity "WAF" "localhost" "8082"
fi

# Test Fail2ban
test_service "Fail2ban" "fail2ban"
if [ $? -eq 0 ]; then
    echo "  📊 Active Jails: $(docker exec trustvault-fail2ban fail2ban-client status 2>/dev/null | grep 'Number of jail' | awk '{print $4}' || echo '0')"
fi

# Test Suricata IDS
test_service "Suricata IDS" "suricata"
if [ $? -eq 0 ]; then
    echo "  📊 Rules Status: $(docker logs trustvault-suricata 2>/dev/null | grep -o '[0-9]* signatures processed' | tail -1 || echo 'No rules loaded')"
fi

# Test Wazuh SIEM
test_service "Wazuh Manager" "wazuh-manager"
test_service "Wazuh Indexer" "wazuh-indexer"
test_service "Wazuh Dashboard" "wazuh-dashboard"
if [ $? -eq 0 ]; then
    test_connectivity "Wazuh Dashboard" "localhost" "5601"
fi

# Test Vault
test_service "HashiCorp Vault" "vault"
if [ $? -eq 0 ]; then
    echo "  📊 Vault Status: $(docker exec trustvault-vault sh -c 'VAULT_ADDR=http://127.0.0.1:8200 vault status' 2>/dev/null | grep 'Initialized' | awk '{print $2}' || echo 'Unknown')"
fi

echo ""
echo "🔍 Testing Monitoring & Alerting"
echo "================================"

# Test Prometheus
test_service "Prometheus" "prometheus"
if [ $? -eq 0 ]; then
    test_connectivity "Prometheus" "localhost" "9090"
fi

# Test Grafana
test_service "Grafana" "grafana"
if [ $? -eq 0 ]; then
    test_connectivity "Grafana" "localhost" "3001"
fi

# Test AlertManager
test_service "AlertManager" "alertmanager"
if [ $? -eq 0 ]; then
    test_connectivity "AlertManager" "localhost" "9093"
fi

echo ""
echo "🔍 Testing Application Security"
echo "==============================="

# Test Django Security Features
test_service "Django Application" "django"
if [ $? -eq 0 ]; then
    echo "  📊 Security Middleware: $(docker exec trustvault-django python manage.py check --deploy 2>/dev/null | grep -c 'WARNINGS' || echo 'Unknown')"
    echo "  📊 User Count: $(docker exec trustvault-django python manage.py shell -c 'from apps.authentication.models import User; print(User.objects.count())' 2>/dev/null || echo 'Unknown')"
fi

# Test Redis Security
test_service "Redis Cache" "redis"

# Test PostgreSQL Security
test_service "PostgreSQL Database" "postgres"

echo ""
echo "🔍 Testing Network Security"
echo "==========================="

# Test Nginx Security Headers
test_service "Nginx Reverse Proxy" "nginx"
if [ $? -eq 0 ]; then
    test_connectivity "Nginx HTTP" "localhost" "80"
    test_connectivity "Nginx HTTPS" "localhost" "443"
fi

echo ""
echo "🔍 Security Configuration Summary"
echo "================================="

echo "📋 Security Services Status:"
echo "  - ModSecurity WAF: $(docker-compose ps modsecurity | grep -q 'Up' && echo '✅ Active' || echo '❌ Inactive')"
echo "  - Fail2ban IPS: $(docker-compose ps fail2ban | grep -q 'Up' && echo '✅ Active' || echo '❌ Inactive')"
echo "  - Suricata IDS: $(docker-compose ps suricata | grep -q 'Up' && echo '✅ Active' || echo '❌ Inactive')"
echo "  - Wazuh SIEM: $(docker-compose ps wazuh-manager | grep -q 'Up' && echo '✅ Active' || echo '❌ Inactive')"
echo "  - Vault Secrets: $(docker-compose ps vault | grep -q 'Up' && echo '✅ Active' || echo '❌ Inactive')"

echo ""
echo "📋 Monitoring Services Status:"
echo "  - Prometheus: $(docker-compose ps prometheus | grep -q 'Up' && echo '✅ Active' || echo '❌ Inactive')"
echo "  - Grafana: $(docker-compose ps grafana | grep -q 'Up' && echo '✅ Active' || echo '❌ Inactive')"
echo "  - AlertManager: $(docker-compose ps alertmanager | grep -q 'Up' && echo '✅ Active' || echo '❌ Inactive')"

echo ""
echo "🎯 Security Recommendations:"
echo "  1. Configure Suricata rules for better threat detection"
echo "  2. Set up Fail2ban jails for active protection"
echo "  3. Initialize and configure Vault for secrets management"
echo "  4. Configure Wazuh indexer certificates"
echo "  5. Set up security alerting in Grafana"

echo ""
echo "✅ Security testing completed!"
