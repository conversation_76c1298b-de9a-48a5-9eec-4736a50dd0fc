# TrustVault - Test Settings

# Inherit from main settings module
from ..settings import *

# Mark tests flag for middleware to soften blocking
TESTING = True

# Test database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }
}

# Disable migrations for faster tests
class DisableMigrations:
    def __contains__(self, item):
        return True
    
    def __getitem__(self, item):
        return None

MIGRATION_MODULES = DisableMigrations()

# Test cache
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    }
}

# Disable logging during tests
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'null': {
            'class': 'logging.NullHandler',
        },
    },
    'root': {
        'handlers': ['null'],
    },
}

# Test email backend
EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'

# Disable Celery during tests
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# Test media files
MEDIA_ROOT = '/tmp/trustvault_test_media'

# Disable security features for testing
SECURE_SSL_REDIRECT = False
SECURE_HSTS_SECONDS = 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False

# Test secrets
SECRET_KEY = 'test-secret-key-for-testing-only'
JWT_SECRET_KEY = 'test-jwt-secret-key'
ENCRYPTION_KEY = 'test-encryption-key-32-characters'

# Adjust DRF pagination for tests: return plain lists (no pagination wrappers)
REST_FRAMEWORK.update({
    'DEFAULT_PAGINATION_CLASS': None,
})
# Keep throttle behavior from base settings so login rate limit tests work
RATELIMIT_ENABLE = False

# Test password validation (less strict for testing)
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 8,
        }
    },
]

# Fast password hashing for tests
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]
