# TrustVault Startup Script
# This script starts all necessary services for the TrustVault application

param(
    [switch]$Build,
    [switch]$Clean,
    [switch]$Logs,
    [switch]$Stop,
    [switch]$Status
)

Write-Host "🛡️  TrustVault - Cybersecurity Portfolio Management System" -ForegroundColor Green
Write-Host "=========================================================" -ForegroundColor Green
Write-Host "Enterprise-grade security infrastructure with SIEM, IDS/IPS, WAF" -ForegroundColor Cyan

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to check if Docker is running
function Test-DockerRunning {
    try {
        docker info | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Function to stop all services
function Stop-TrustVault {
    Write-Host "Stopping TrustVault services..." -ForegroundColor Yellow
    docker-compose down --remove-orphans
    Write-Host "All services stopped." -ForegroundColor Green
}

# Function to show service status
function Show-Status {
    Write-Host "TrustVault Service Status:" -ForegroundColor Cyan
    docker-compose ps
}

# Function to show logs
function Show-Logs {
    Write-Host "Showing TrustVault logs (press Ctrl+C to exit):" -ForegroundColor Cyan
    docker-compose logs -f
}

# Function to clean up
function Clean-TrustVault {
    Write-Host "Cleaning up TrustVault..." -ForegroundColor Yellow
    docker-compose down --volumes --remove-orphans
    docker system prune -f
    Write-Host "Cleanup completed." -ForegroundColor Green
}

# Function to build and start services
function Start-TrustVault {
    param([bool]$BuildImages = $false)
    
    Write-Host "Starting TrustVault services..." -ForegroundColor Cyan
    
    # Check if Docker is running
    if (-not (Test-DockerRunning)) {
        Write-Host "Error: Docker is not running. Please start Docker Desktop." -ForegroundColor Red
        exit 1
    }
    
    # Create comprehensive security infrastructure directories
    Write-Host "Creating security infrastructure directories..." -ForegroundColor Yellow
    $directories = @(
        "logs/nginx", "logs/django", "logs/postgres", "logs/redis",
        "logs/wazuh", "logs/suricata", "logs/modsecurity", "logs/fail2ban", "logs/celery",
        "vault/config", "vault/data", "wazuh/indexer", "suricata/config", "suricata/rules",
        "grafana/provisioning", "grafana/dashboards"
    )

    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    try {
        if ($BuildImages) {
            Write-Host "Building Docker images..." -ForegroundColor Yellow
            docker-compose build --no-cache
        }
        
        # Start the services
        Write-Host "Starting services..." -ForegroundColor Yellow
        docker-compose up -d
        
        # Wait for services to be ready
        Write-Host "Waiting for services to start..." -ForegroundColor Yellow
        Start-Sleep -Seconds 10
        
        # Check service status
        Write-Host "Service Status:" -ForegroundColor Cyan
        docker-compose ps
        
        Write-Host ""
        Write-Host "🎉 TrustVault Cybersecurity Infrastructure is running!" -ForegroundColor Green
        Write-Host "=====================================================" -ForegroundColor Green
        Write-Host ""
        Write-Host "📱 Application Services:" -ForegroundColor Cyan
        Write-Host "   Frontend:     http://localhost:3000" -ForegroundColor White
        Write-Host "   Backend API:  http://localhost:8000" -ForegroundColor White
        Write-Host "   API Docs:     http://localhost:8000/api/docs/" -ForegroundColor White
        Write-Host ""
        Write-Host "🛡️  Security & Monitoring:" -ForegroundColor Cyan
        Write-Host "   Wazuh SIEM:   http://localhost:5601" -ForegroundColor White
        Write-Host "   Grafana:      http://localhost:3001 (admin/grafana_dev_password_2024)" -ForegroundColor White
        Write-Host "   Prometheus:   http://localhost:9090" -ForegroundColor White
        Write-Host "   AlertManager: http://localhost:9093" -ForegroundColor White
        Write-Host ""
        Write-Host "📊 Monitoring Exporters:" -ForegroundColor Cyan
        Write-Host "   Node Metrics: http://localhost:9100" -ForegroundColor White
        Write-Host "   DB Metrics:   http://localhost:9187" -ForegroundColor White
        Write-Host "   Cache Metrics: http://localhost:9121" -ForegroundColor White
        Write-Host ""
        Write-Host "🔧 Infrastructure:" -ForegroundColor Cyan
        Write-Host "   PostgreSQL:   localhost:5432" -ForegroundColor White
        Write-Host "   Redis:        localhost:6379" -ForegroundColor White
        Write-Host "   Vault:        http://localhost:8200" -ForegroundColor White
        Write-Host ""
        Write-Host "✅ Cybersecurity Infrastructure Active:" -ForegroundColor Green
        Write-Host "   • SIEM (Wazuh) - Security Information & Event Management" -ForegroundColor Gray
        Write-Host "   • IDS/IPS (Suricata) - Intrusion Detection & Prevention" -ForegroundColor Gray
        Write-Host "   • WAF (ModSecurity) - Web Application Firewall" -ForegroundColor Gray
        Write-Host "   • DDoS Protection (Fail2Ban) - Brute Force Prevention" -ForegroundColor Gray
        Write-Host "   • Secrets Management (Vault) - Encrypted Key Storage" -ForegroundColor Gray
        Write-Host "   • Real-time Monitoring (Prometheus/Grafana)" -ForegroundColor Gray
        Write-Host ""
        Write-Host "💡 Management Commands:" -ForegroundColor Yellow
        Write-Host "   View logs:    docker-compose logs -f [service]" -ForegroundColor White
        Write-Host "   Stop all:     ./start-trustvault.ps1 -Stop" -ForegroundColor White
        Write-Host "   Status:       ./start-trustvault.ps1 -Status" -ForegroundColor White
        Write-Host "   Clean up:     ./start-trustvault.ps1 -Clean" -ForegroundColor White
    }
    catch {
        Write-Host "Error starting TrustVault: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Main script logic
if ($Stop) {
    Stop-TrustVault
}
elseif ($Status) {
    Show-Status
}
elseif ($Logs) {
    Show-Logs
}
elseif ($Clean) {
    Clean-TrustVault
}
else {
    Start-TrustVault -BuildImages $Build
}
