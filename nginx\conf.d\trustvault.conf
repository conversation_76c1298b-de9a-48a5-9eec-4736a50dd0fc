# Rate limiting zones are defined in main nginx.conf

# HTTP to HTTPS redirect - Force HTTPS only
server {
    listen 80;
    server_name localhost trustvault.local www.trustvault.local api.trustvault.local;

    # Health check endpoint (only endpoint allowed on HTTP)
    location /health {
        access_log off;
        return 200 "nginx-security-proxy-healthy\n";
        add_header Content-Type text/plain;
    }

    # Redirect all other traffic to HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# HTTPS server
server {
    listen 443 ssl;
    http2 on;
    server_name localhost trustvault.local www.trustvault.local api.trustvault.local;

    # DNS resolver for dynamic upstream resolution
    resolver 127.0.0.11 valid=30s;

    # SSL Configuration
    ssl_certificate /etc/nginx/ssl/trustvault.crt;
    ssl_certificate_key /etc/nginx/ssl/trustvault.key;
    ssl_dhparam /etc/nginx/ssl/dhparam.pem;

    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_timeout 10m;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;

    # Enhanced Security Headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none';" always;
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;
    add_header X-Permitted-Cross-Domain-Policies "none" always;

    # Remove server signature
    server_tokens off;

    # Rate limiting
    limit_req zone=general burst=50 nodelay;

    location /health {
        access_log off;
        return 200 "nginx-security-proxy-healthy\n";
        add_header Content-Type text/plain;
    }

    location /api/ {
        # API rate limiting
        limit_req zone=api burst=20 nodelay;

        set $upstream_django trustvault-django:8000;
        proxy_pass http://$upstream_django;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # API-specific security headers
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;
    }

    # Authentication endpoints with stricter rate limiting
    location ~ ^/(api/auth|api/login|admin) {
        limit_req zone=auth_limit burst=10 nodelay;

        set $upstream_django trustvault-django:8000;
        proxy_pass http://$upstream_django;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;

        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;
    }



    # Monitoring Services - Re-enabled with dynamic DNS resolution
    location /grafana/ {
        set $upstream_grafana trustvault-grafana:3000;
        proxy_pass http://$upstream_grafana/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;

        # WebSocket support for Grafana
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Grafana specific headers
        proxy_set_header X-Forwarded-Server $host;
        proxy_redirect off;
    }

    location /prometheus/ {
        set $upstream_prometheus trustvault-prometheus:9090;
        proxy_pass http://$upstream_prometheus/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Handle Prometheus console templates
        proxy_redirect off;
    }



    location /alertmanager/ {
        set $upstream_alertmanager trustvault-alertmanager:9093;
        proxy_pass http://$upstream_alertmanager/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # AlertManager specific configuration
        proxy_redirect off;
    }

    # Security Services
    location /vault/ {
        set $upstream_vault trustvault-vault:8200;
        proxy_pass http://$upstream_vault/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Vault specific headers
        proxy_redirect off;
        proxy_buffering off;

        # WebSocket support for Vault UI
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location /wazuh/ {
        set $upstream_wazuh trustvault-wazuh-dashboard:5601;
        proxy_pass http://$upstream_wazuh/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket support for Wazuh
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Wazuh specific headers
        proxy_redirect off;
        proxy_buffering off;
    }

    location /waf/ {
        set $upstream_waf trustvault-waf:8080;
        proxy_pass http://$upstream_waf/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WAF management interface
        proxy_redirect off;
    }

    # Metrics Exporters (for debugging/admin access)
    location /metrics/node/ {
        set $upstream_node_exporter trustvault-node-exporter:9100;
        proxy_pass http://$upstream_node_exporter/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /metrics/postgres/ {
        set $upstream_postgres_exporter trustvault-postgres-exporter:9187;
        proxy_pass http://$upstream_postgres_exporter/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /metrics/redis/ {
        set $upstream_redis_exporter trustvault-redis-exporter:9121;
        proxy_pass http://$upstream_redis_exporter/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /metrics/nginx/ {
        set $upstream_nginx_exporter trustvault-nginx-exporter:9113;
        proxy_pass http://$upstream_nginx_exporter/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /metrics/blackbox/ {
        set $upstream_blackbox_exporter trustvault-blackbox-exporter:9115;
        proxy_pass http://$upstream_blackbox_exporter/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # React static files - serve directly from React container
    location /static/ {
        set $upstream_react trustvault-react:80;
        proxy_pass http://$upstream_react;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Cache static files
        expires 1y;
        add_header Cache-Control "public, immutable" always;
    }

    # Wazuh SIEM Demo Interface
    location /siem-demo/ {
        alias /usr/share/nginx/html/wazuh-demo/;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # React app - serve all other requests
    location / {
        set $upstream_react trustvault-react:80;
        proxy_pass http://$upstream_react;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;

        # Proxy timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Preserve content type from upstream
        proxy_pass_header Content-Type;
    }
}
