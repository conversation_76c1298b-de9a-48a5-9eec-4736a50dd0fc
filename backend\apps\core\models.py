# TrustVault - Core Models

import uuid
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone


class BaseModel(models.Model):
    """Base model with common fields for all models."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        abstract = True


class AuditLog(BaseModel):
    """Model for audit logging."""
    
    ACTION_CHOICES = [
        ('CREATE', 'Create'),
        ('UPDATE', 'Update'),
        ('DELETE', 'Delete'),
        ('LOGIN', 'Login'),
        ('LOGOUT', 'Logout'),
        ('ACCESS', 'Access'),
        ('EXPORT', 'Export'),
        ('IMPORT', 'Import'),
    ]
    
    SEVERITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]
    
    user = models.ForeignKey(
        get_user_model(),
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='audit_logs'
    )
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    resource_type = models.CharField(max_length=100)
    resource_id = models.CharField(max_length=100, null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    details = models.JSONField(default=dict)
    severity = models.CharField(max_length=10, choices=SEVERITY_CHOICES, default='LOW')
    timestamp = models.DateTimeField(default=timezone.now)
    
    class Meta:
        db_table = 'core_audit_log'
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['action', 'timestamp']),
            models.Index(fields=['resource_type', 'timestamp']),
            models.Index(fields=['severity', 'timestamp']),
        ]
    
    def __str__(self):
        # Tests expect: "email - ACTION - Resource:ID"
        user_str = self.user.email if self.user else "None"
        rid = f":{self.resource_id}" if self.resource_id else ""
        return f"{user_str} - {self.action} - {self.resource_type}{rid}"


class SystemConfiguration(BaseModel):
    """Model for system configuration."""
    
    key = models.CharField(max_length=100, unique=True)
    value = models.TextField()
    description = models.TextField(blank=True)
    is_encrypted = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'core_system_configuration'
        ordering = ['key']
    
    def __str__(self):
        return f"{self.key}: {self.value[:50]}..."


class SecurityEvent(BaseModel):
    """Model for security events."""
    
    EVENT_TYPES = [
        ('FAILED_LOGIN', 'Failed Login'),
        ('SUSPICIOUS_ACTIVITY', 'Suspicious Activity'),
        ('UNAUTHORIZED_ACCESS', 'Unauthorized Access'),
        ('DATA_BREACH', 'Data Breach'),
        ('MALWARE_DETECTED', 'Malware Detected'),
        ('INTRUSION_ATTEMPT', 'Intrusion Attempt'),
    ]
    
    RISK_LEVELS = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]
    
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES)
    risk_level = models.CharField(max_length=10, choices=RISK_LEVELS)
    source_ip = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    description = models.TextField()
    details = models.JSONField(default=dict)
    is_resolved = models.BooleanField(default=False)
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(
        get_user_model(),
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_security_events'
    )
    
    class Meta:
        db_table = 'core_security_event'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['event_type', 'created_at']),
            models.Index(fields=['risk_level', 'created_at']),
            models.Index(fields=['source_ip', 'created_at']),
            models.Index(fields=['is_resolved', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.event_type} - {self.risk_level} - {self.created_at}"


class DataRetentionPolicy(BaseModel):
    """Model for data retention policies."""
    
    resource_type = models.CharField(max_length=100, unique=True)
    retention_days = models.PositiveIntegerField()
    description = models.TextField()
    is_gdpr_related = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'core_data_retention_policy'
        verbose_name_plural = 'Data Retention Policies'
    
    def __str__(self):
        return f"{self.resource_type} - {self.retention_days} days"
