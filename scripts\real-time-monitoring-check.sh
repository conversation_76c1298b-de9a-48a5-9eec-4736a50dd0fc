#!/bin/bash
# TrustVault Real-Time Monitoring System Status Check
# Comprehensive verification of all security monitoring components

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_header() { echo -e "${PURPLE}$1${NC}"; }

show_banner() {
    clear
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════════╗"
    echo "║           TrustVault Real-Time Monitoring Status Check          ║"
    echo "║              Security Systems Verification                      ║"
    echo "╚══════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
}

# Check Docker Services Status
check_docker_services() {
    log_header "🐳 DOCKER SERVICES STATUS"
    echo "=========================="

    local services=(
        "trustvault-suricata:IDS"
        "trustvault-fail2ban:IPS"
        "trustvault-grafana:Monitoring"
        "trustvault-prometheus:Metrics"
        "trustvault-wazuh-manager:SIEM"
        "trustvault-wazuh-dashboard:SIEM UI"
        "trustvault-alertmanager:Alerting"
        "trustvault-nginx:Proxy"
        "trustvault-waf:WAF"
    )

    local running=0
    local total=${#services[@]}

    for service_info in "${services[@]}"; do
        IFS=':' read -r service_name service_type <<< "$service_info"

        if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$service_name.*Up"; then
            log_success "✅ $service_type ($service_name): Running"
            ((running++))
        elif docker ps -a --format "table {{.Names}}\t{{.Status}}" | grep -q "$service_name.*Restarting"; then
            log_warning "⚠️  $service_type ($service_name): Restarting"
        else
            log_error "❌ $service_type ($service_name): Not running"
        fi
    done

    echo ""
    log_info "Services Status: $running/$total running"
    echo ""
}

# Check Prometheus Metrics Collection
check_prometheus() {
    log_header "📊 PROMETHEUS METRICS COLLECTION"
    echo "================================="

    if curl -s http://localhost:9090/api/v1/query?query=up > /dev/null 2>&1; then
        log_success "✅ Prometheus API: Accessible"

        # Check specific metrics
        local metrics_response=$(curl -s "http://localhost:9090/api/v1/query?query=up")
        local up_services=$(echo "$metrics_response" | grep -o '"value":\[.*,"1"\]' | wc -l)
        local down_services=$(echo "$metrics_response" | grep -o '"value":\[.*,"0"\]' | wc -l)

        log_info "Services monitored: $((up_services + down_services))"
        log_success "✅ Services UP: $up_services"
        if [ "$down_services" -gt 0 ]; then
            log_warning "⚠️  Services DOWN: $down_services"
        fi

        # Check if nginx metrics are available
        if curl -s "http://localhost:9090/api/v1/query?query=nginx_up" | grep -q '"value"'; then
            log_success "✅ Nginx metrics: Available"
        else
            log_warning "⚠️  Nginx metrics: Limited"
        fi

    else
        log_error "❌ Prometheus API: Not accessible"
    fi
    echo ""
}

# Check Grafana Dashboard
check_grafana() {
    log_header "📈 GRAFANA MONITORING DASHBOARD"
    echo "==============================="

    if curl -s http://localhost:3001/api/health > /dev/null 2>&1; then
        log_success "✅ Grafana API: Accessible"

        local health_response=$(curl -s http://localhost:3001/api/health)
        if echo "$health_response" | grep -q '"database":"ok"'; then
            log_success "✅ Grafana Database: Connected"
        else
            log_warning "⚠️  Grafana Database: Issues detected"
        fi

        log_info "Dashboard URL: http://localhost:3001"

    else
        log_error "❌ Grafana: Not accessible"
    fi
    echo ""
}

# Check Suricata IDS
check_suricata_ids() {
    log_header "🔍 SURICATA IDS (INTRUSION DETECTION)"
    echo "====================================="

    if docker ps | grep -q "trustvault-suricata.*Up"; then
        log_success "✅ Suricata IDS: Running"

        # Check if rules are loaded
        local suricata_logs=$(docker logs trustvault-suricata --tail 20 2>/dev/null)
        if echo "$suricata_logs" | grep -q "Engine started"; then
            log_success "✅ Suricata Engine: Started"

            local rules_count=$(echo "$suricata_logs" | grep -o "[0-9]* rules successfully loaded" | head -1 | grep -o "[0-9]*")
            if [ -n "$rules_count" ] && [ "$rules_count" -gt 0 ]; then
                log_success "✅ Detection Rules: $rules_count rules loaded"
            else
                log_warning "⚠️  Detection Rules: Count unknown"
            fi
        else
            log_warning "⚠️  Suricata Engine: Starting up"
        fi

        # Test detection capability
        log_info "Testing IDS detection capability..."
        curl -s -H "User-Agent: sqlmap/test" http://localhost/health > /dev/null 2>&1
        sleep 2

        if docker logs trustvault-suricata --tail 5 2>/dev/null | grep -q "sqlmap\|alert\|ATTACK"; then
            log_success "✅ Real-time Detection: Working"
        else
            log_warning "⚠️  Real-time Detection: Limited visibility"
        fi

    else
        log_error "❌ Suricata IDS: Not running"
    fi
    echo ""
}

# Check Fail2Ban IPS
check_fail2ban_ips() {
    log_header "🚫 FAIL2BAN IPS (INTRUSION PREVENTION)"
    echo "======================================"

    if docker ps | grep -q "trustvault-fail2ban.*Up"; then
        log_success "✅ Fail2Ban IPS: Running"

        # Check jail status
        local jail_status=$(docker exec trustvault-fail2ban fail2ban-client status 2>/dev/null || echo "")
        if echo "$jail_status" | grep -q "Jail list:"; then
            local active_jails=$(echo "$jail_status" | grep "Jail list:" | cut -d: -f2 | tr ',' '\n' | wc -l)
            log_success "✅ Active Jails: $active_jails configured"
        else
            log_warning "⚠️  Jail Status: Unable to verify"
        fi

        # Test blocking capability
        log_info "Testing IPS blocking capability..."
        for i in {1..6}; do
            curl -s -o /dev/null "http://localhost/fake-login" 2>/dev/null || true
        done
        sleep 2

        log_info "IPS Response: Monitoring for blocks"

    elif docker ps -a | grep -q "trustvault-fail2ban.*Restarting"; then
        log_warning "⚠️  Fail2Ban IPS: Restarting (configuration issues possible)"
    else
        log_error "❌ Fail2Ban IPS: Not running"
    fi
    echo ""
}

# Check Wazuh SIEM
check_wazuh_siem() {
    log_header "🛡️  WAZUH SIEM (SECURITY INFORMATION & EVENT MANAGEMENT)"
    echo "========================================================"

    if docker ps | grep -q "trustvault-wazuh-manager.*Up"; then
        log_success "✅ Wazuh Manager: Running"

        # Check indexer connection
        local wazuh_logs=$(docker logs trustvault-wazuh-manager --tail 10 2>/dev/null)
        if echo "$wazuh_logs" | grep -q "OpenSearch Security not initialized"; then
            log_warning "⚠️  Wazuh Indexer: Initializing"
        elif echo "$wazuh_logs" | grep -q "Connected to"; then
            log_success "✅ Wazuh Indexer: Connected"
        else
            log_warning "⚠️  Wazuh Indexer: Status unknown"
        fi

    else
        log_error "❌ Wazuh Manager: Not running"
    fi

    # Check Wazuh Dashboard
    if curl -s http://localhost:5601/api/status 2>/dev/null | grep -q "ready"; then
        log_success "✅ Wazuh Dashboard: Ready"
        log_info "SIEM URL: http://localhost:5601"
    elif curl -s http://localhost:5601 > /dev/null 2>&1; then
        log_warning "⚠️  Wazuh Dashboard: Starting up"
    else
        log_error "❌ Wazuh Dashboard: Not accessible"
    fi
    echo ""
}

# Check AlertManager
check_alertmanager() {
    log_header "🚨 ALERTMANAGER (NOTIFICATION SYSTEM)"
    echo "====================================="

    if docker ps | grep -q "trustvault-alertmanager.*Up"; then
        log_success "✅ AlertManager: Running"

        if curl -s http://localhost:9093/api/v1/status > /dev/null 2>&1; then
            log_success "✅ AlertManager API: Accessible"
            log_info "Alerts URL: http://localhost:9093"
        else
            log_warning "⚠️  AlertManager API: Not accessible"
        fi

    elif docker ps -a | grep -q "trustvault-alertmanager.*Restarting"; then
        log_warning "⚠️  AlertManager: Restarting"
    else
        log_error "❌ AlertManager: Not running"
    fi
    echo ""
}

# Overall System Assessment
system_assessment() {
    log_header "🎯 OVERALL SYSTEM ASSESSMENT"
    echo "============================"

    local total_checks=6
    local working_systems=0

    # Count working systems
    docker ps | grep -q "trustvault-suricata.*Up" && ((working_systems++))
    docker ps | grep -q "trustvault-grafana.*Up" && ((working_systems++))
    curl -s http://localhost:9090/api/v1/query?query=up > /dev/null 2>&1 && ((working_systems++))
    docker ps | grep -q "trustvault-wazuh-manager.*Up" && ((working_systems++))
    curl -s http://localhost:3001/api/health > /dev/null 2>&1 && ((working_systems++))
    docker ps | grep -q "trustvault-nginx.*Up" && ((working_systems++))

    local effectiveness=$((working_systems * 100 / total_checks))

    echo ""
    if [ "$effectiveness" -ge 90 ]; then
        log_success "🏆 SYSTEM STATUS: EXCELLENT ($effectiveness%)"
        log_success "✅ Real-time monitoring is working effectively"
    elif [ "$effectiveness" -ge 70 ]; then
        log_warning "⚠️  SYSTEM STATUS: GOOD ($effectiveness%)"
        log_warning "⚠️  Some components need attention"
    elif [ "$effectiveness" -ge 50 ]; then
        log_warning "⚠️  SYSTEM STATUS: FAIR ($effectiveness%)"
        log_warning "⚠️  Multiple components need fixing"
    else
        log_error "❌ SYSTEM STATUS: POOR ($effectiveness%)"
        log_error "❌ Major issues detected - immediate attention required"
    fi

    echo ""
    log_info "📊 MONITORING SYSTEM SUMMARY:"
    echo "   • IDS (Intrusion Detection): $(docker ps | grep -q "trustvault-suricata.*Up" && echo "✅ Active" || echo "❌ Inactive")"
    echo "   • IPS (Intrusion Prevention): $(docker ps | grep -q "trustvault-fail2ban.*Up" && echo "✅ Active" || echo "❌ Inactive")"
    echo "   • SIEM (Security Analytics): $(docker ps | grep -q "trustvault-wazuh-manager.*Up" && echo "✅ Active" || echo "❌ Inactive")"
    echo "   • Metrics Collection: $(curl -s http://localhost:9090/api/v1/query?query=up > /dev/null 2>&1 && echo "✅ Active" || echo "❌ Inactive")"
    echo "   • Dashboard Monitoring: $(curl -s http://localhost:3001/api/health > /dev/null 2>&1 && echo "✅ Active" || echo "❌ Inactive")"
    echo "   • Alert System: $(docker ps | grep -q "trustvault-alertmanager.*Up" && echo "✅ Active" || echo "❌ Inactive")"

    echo ""
    log_info "🔗 ACCESS POINTS:"
    echo "   • Grafana Dashboard: http://localhost:3001"
    echo "   • Prometheus Metrics: http://localhost:9090"
    echo "   • Wazuh SIEM: http://localhost:5601"
    echo "   • AlertManager: http://localhost:9093"
}

# Main execution
main() {
    show_banner

    log_info "Starting comprehensive real-time monitoring system check..."
    echo ""

    check_docker_services
    check_prometheus
    check_grafana
    check_suricata_ids
    check_fail2ban_ips
    check_wazuh_siem
    check_alertmanager
    system_assessment

    echo ""
    log_info "🎯 Real-time monitoring system check completed!"
    echo ""
}

# Execute main function
main "$@"