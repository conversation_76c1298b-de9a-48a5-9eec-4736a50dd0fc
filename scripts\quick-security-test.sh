#!/bin/bash
# Quick Security Test - Verify 404 Fix

echo "🔍 TrustVault Quick Security Test"
echo "================================="
echo ""

TARGET="https://localhost"

echo "Testing security endpoints..."
echo ""

# Test 1: Security Test Endpoint
echo "1. Testing /api/test/ endpoint:"
response=$(curl -k -s -o /dev/null -w "%{http_code}" "$TARGET/api/test/")
echo "   Normal request: HTTP $response"

response=$(curl -k -s -o /dev/null -w "%{http_code}" "$TARGET/api/test/?id=1%27%20OR%20%271%27%3D%271")
echo "   SQL injection: HTTP $response (should be 403)"
echo ""

# Test 2: Search Test Endpoint  
echo "2. Testing /api/search/ endpoint:"
response=$(curl -k -s -o /dev/null -w "%{http_code}" "$TARGET/api/search/")
echo "   Normal request: HTTP $response"

response=$(curl -k -s -o /dev/null -w "%{http_code}" "$TARGET/api/search/?q=%3Cscript%3Ealert%28%27xss%27%29%3C%2Fscript%3E")
echo "   XSS attack: HTTP $response (should be 403)"
echo ""

# Test 3: Ping Test Endpoint
echo "3. Testing /api/ping/ endpoint:"
response=$(curl -k -s -o /dev/null -w "%{http_code}" "$TARGET/api/ping/")
echo "   Normal request: HTTP $response"

response=$(curl -k -s -o /dev/null -w "%{http_code}" "$TARGET/api/ping/?host=127.0.0.1%3Bcat%20%2Fetc%2Fpasswd")
echo "   Command injection: HTTP $response (should be 403)"
echo ""

# Test 4: Security Headers
echo "4. Testing security headers:"
headers=$(curl -k -s -I "$TARGET/api/test/")

if echo "$headers" | grep -q "X-Frame-Options"; then
    echo "   ✅ X-Frame-Options: Present"
else
    echo "   ❌ X-Frame-Options: Missing"
fi

if echo "$headers" | grep -q "X-Content-Type-Options"; then
    echo "   ✅ X-Content-Type-Options: Present"
else
    echo "   ❌ X-Content-Type-Options: Missing"
fi

if echo "$headers" | grep -q "X-XSS-Protection"; then
    echo "   ✅ X-XSS-Protection: Present"
else
    echo "   ❌ X-XSS-Protection: Missing"
fi

if echo "$headers" | grep -q "Strict-Transport-Security"; then
    echo "   ✅ HSTS: Present"
else
    echo "   ❌ HSTS: Missing"
fi

echo ""
echo "✅ Security test completed!"
echo "🔒 All endpoints are now accessible and security middleware is working."
