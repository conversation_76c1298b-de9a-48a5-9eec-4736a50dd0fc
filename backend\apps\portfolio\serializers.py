# TrustVault - Portfolio Serializers

from rest_framework import serializers
from decimal import Decimal
from .models import Portfolio, Asset, Holding, Transaction, Watchlist, WatchlistItem


class AssetSerializer(serializers.ModelSerializer):
    """Serializer for Asset model."""
    
    class Meta:
        model = Asset
        fields = [
            'id', 'symbol', 'name', 'asset_type', 'description',
            'current_price', 'currency', 'market_cap', 'volume_24h',
            'change_24h', 'sector', 'country', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class HoldingSerializer(serializers.ModelSerializer):
    """Serializer for Holding model."""
    
    asset = AssetSerializer(read_only=True)
    asset_id = serializers.UUIDField(write_only=True)
    profit_loss = serializers.DecimalField(max_digits=15, decimal_places=2, read_only=True)
    profit_loss_percentage = serializers.DecimalField(max_digits=10, decimal_places=4, read_only=True)
    
    class Meta:
        model = Holding
        fields = [
            'id', 'asset', 'asset_id', 'quantity', 'average_cost',
            'current_value', 'profit_loss', 'profit_loss_percentage',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'current_value', 'created_at', 'updated_at']
    
    def validate_quantity(self, value):
        """Validate quantity is positive."""
        if value <= 0:
            raise serializers.ValidationError("Quantity must be positive.")
        return value
    
    def validate_average_cost(self, value):
        """Validate average cost is positive."""
        if value <= 0:
            raise serializers.ValidationError("Average cost must be positive.")
        return value


class TransactionSerializer(serializers.ModelSerializer):
    """Serializer for Transaction model."""
    
    asset = AssetSerializer(read_only=True)
    asset_id = serializers.UUIDField(write_only=True, required=False, allow_null=True)
    
    class Meta:
        model = Transaction
        fields = [
            'id', 'asset', 'asset_id', 'transaction_type', 'quantity',
            'price', 'total_amount', 'fees', 'notes', 'transaction_date',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate(self, attrs):
        """Validate transaction data."""
        transaction_type = attrs.get('transaction_type')
        quantity = attrs.get('quantity')
        price = attrs.get('price')
        asset_id = attrs.get('asset_id')
        
        # For buy/sell transactions, asset, quantity, and price are required
        if transaction_type in ['BUY', 'SELL']:
            if not asset_id:
                raise serializers.ValidationError("Asset is required for buy/sell transactions.")
            if not quantity or quantity <= 0:
                raise serializers.ValidationError("Quantity must be positive for buy/sell transactions.")
            if not price or price <= 0:
                raise serializers.ValidationError("Price must be positive for buy/sell transactions.")
        
        # For cash transactions, asset should not be provided
        if transaction_type in ['DEPOSIT', 'WITHDRAWAL']:
            if asset_id:
                raise serializers.ValidationError("Asset should not be provided for cash transactions.")
        
        return attrs


class PortfolioSerializer(serializers.ModelSerializer):
    """Serializer for Portfolio model."""
    
    holdings = HoldingSerializer(many=True, read_only=True)
    holdings_count = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = Portfolio
        fields = [
            'id', 'name', 'description', 'portfolio_type', 'total_value',
            'currency', 'is_public', 'holdings', 'holdings_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'total_value', 'created_at', 'updated_at']
    
    def validate_name(self, value):
        """Validate portfolio name is unique for user and safe (no HTML/script)."""
        # Reject obvious HTML/script tags to prevent XSS in tests and API
        if '<' in value or '>' in value or 'script' in value.lower():
            raise serializers.ValidationError("Invalid characters in name.")
        user = self.context['request'].user
        if Portfolio.objects.filter(user=user, name=value).exclude(pk=self.instance.pk if self.instance else None).exists():
            raise serializers.ValidationError("Portfolio with this name already exists.")
        return value

    def validate_description(self, value):
        """Prevent excessively large payloads and basic script injection in description."""
        if value and len(value) > 5000:
            raise serializers.ValidationError("Description is too long.")
        if value and ('<script' in value.lower() or '</script>' in value.lower()):
            raise serializers.ValidationError("Invalid content in description.")
        return value


class PortfolioSummarySerializer(serializers.ModelSerializer):
    """Simplified serializer for portfolio list view."""
    
    holdings_count = serializers.SerializerMethodField()
    performance_24h = serializers.SerializerMethodField()
    
    class Meta:
        model = Portfolio
        fields = [
            'id', 'name', 'description', 'portfolio_type', 'total_value',
            'currency', 'is_public', 'holdings_count', 'performance_24h',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'total_value', 'created_at', 'updated_at']
    
    def get_holdings_count(self, obj):
        """Get number of holdings in portfolio."""
        return obj.holdings.count()
    
    def get_performance_24h(self, obj):
        """Get 24h performance (simplified calculation)."""
        # This would typically involve historical data
        # For now, return a placeholder
        return {
            'change': Decimal('0.00'),
            'change_percentage': Decimal('0.00')
        }


class WatchlistItemSerializer(serializers.ModelSerializer):
    """Serializer for WatchlistItem model."""
    
    asset = AssetSerializer(read_only=True)
    asset_id = serializers.UUIDField(write_only=True)
    
    class Meta:
        model = WatchlistItem
        fields = [
            'id', 'asset', 'asset_id', 'target_price', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class WatchlistSerializer(serializers.ModelSerializer):
    """Serializer for Watchlist model."""
    
    items = WatchlistItemSerializer(source='watchlistitem_set', many=True, read_only=True)
    items_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Watchlist
        fields = [
            'id', 'name', 'description', 'items', 'items_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_items_count(self, obj):
        """Get number of items in watchlist."""
        return obj.watchlistitem_set.count()
    
    def validate_name(self, value):
        """Validate watchlist name is unique for user."""
        user = self.context['request'].user
        if Watchlist.objects.filter(user=user, name=value).exclude(pk=self.instance.pk if self.instance else None).exists():
            raise serializers.ValidationError("Watchlist with this name already exists.")
        return value


class PortfolioAnalyticsSerializer(serializers.Serializer):
    """Serializer for portfolio analytics data."""
    
    total_value = serializers.DecimalField(max_digits=15, decimal_places=2)
    total_cost = serializers.DecimalField(max_digits=15, decimal_places=2)
    total_profit_loss = serializers.DecimalField(max_digits=15, decimal_places=2)
    total_profit_loss_percentage = serializers.DecimalField(max_digits=10, decimal_places=4)
    
    asset_allocation = serializers.ListField(
        child=serializers.DictField()
    )
    
    sector_allocation = serializers.ListField(
        child=serializers.DictField()
    )
    
    top_performers = serializers.ListField(
        child=serializers.DictField()
    )
    
    worst_performers = serializers.ListField(
        child=serializers.DictField()
    )
