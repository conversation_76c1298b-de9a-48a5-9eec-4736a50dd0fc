import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Tooltip,
  Badge,
  LinearProgress,
  CircularProgress
} from '@mui/material';
import {
  Visibility,
  Block,
  CheckCircle,
  Warning,
  Error,
  FilterList,
  Refresh,
  GetApp,
  Flag,
  Security,
  BugReport
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import api from '../../services/api';

interface ThreatDetection {
  id: number;
  threat_category: string;
  severity: string;
  confidence_score: number;
  source_ip: string;
  source_port: number | null;
  target_ip: string;
  target_port: number | null;
  attack_signature: string;
  action_taken: string;
  is_false_positive: boolean;
  detecting_service: {
    id: number;
    name: string;
    service_type: string;
  };
  geolocation: any;
  created_at: string;
}

const THREAT_CATEGORIES = [
  'MALWARE',
  'INTRUSION',
  'DDOS',
  'BRUTE_FORCE',
  'SQL_INJECTION',
  'XSS',
  'PHISHING',
  'SUSPICIOUS_ACTIVITY',
  'POLICY_VIOLATION',
  'ANOMALY'
];

const SEVERITY_LEVELS = ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'];
const ACTION_TYPES = ['BLOCKED', 'QUARANTINED', 'LOGGED', 'ALERTED', 'IGNORED', 'MANUAL_REVIEW'];

const ThreatDetectionComponent: React.FC = () => {
  const theme = useTheme();
  const [threats, setThreats] = useState<ThreatDetection[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedThreat, setSelectedThreat] = useState<ThreatDetection | null>(null);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [filterDialogOpen, setFilterDialogOpen] = useState(false);
  
  // Filters
  const [filters, setFilters] = useState({
    threat_category: '',
    severity: '',
    action_taken: '',
    is_false_positive: '',
    service_id: '',
    date_from: '',
    date_to: ''
  });

  useEffect(() => {
    loadThreats();
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadThreats, 30000);
    return () => clearInterval(interval);
  }, [filters]);

  const loadThreats = async () => {
    try {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });
      
      const response = await api.get(`/security/cybersecurity/threats/?${params.toString()}`);
      setThreats(response.data);
    } catch (error) {
      console.error('Failed to load threats:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return theme.palette.error.main;
      case 'HIGH': return theme.palette.warning.main;
      case 'MEDIUM': return theme.palette.info.main;
      case 'LOW': return theme.palette.success.main;
      default: return theme.palette.grey[500];
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'BLOCKED': return <Block color="error" />;
      case 'QUARANTINED': return <Security color="warning" />;
      case 'LOGGED': return <BugReport color="info" />;
      case 'ALERTED': return <Warning color="warning" />;
      case 'IGNORED': return <CheckCircle color="disabled" />;
      case 'MANUAL_REVIEW': return <Flag color="primary" />;
      default: return <BugReport />;
    }
  };

  const openThreatDetail = (threat: ThreatDetection) => {
    setSelectedThreat(threat);
    setDetailDialogOpen(true);
  };

  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({ ...prev, [field]: value }));
  };

  const clearFilters = () => {
    setFilters({
      threat_category: '',
      severity: '',
      action_taken: '',
      is_false_positive: '',
      service_id: '',
      date_from: '',
      date_to: ''
    });
  };

  const exportThreats = () => {
    // Implementation for exporting threats data
    console.log('Exporting threats data...');
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" component="h1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Security color="primary" />
          Threat Detection
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<FilterList />}
            onClick={() => setFilterDialogOpen(true)}
            sx={{ mr: 1 }}
          >
            Filters
          </Button>
          <Button
            variant="outlined"
            startIcon={<GetApp />}
            onClick={exportThreats}
            sx={{ mr: 1 }}
          >
            Export
          </Button>
          <Button
            variant="contained"
            startIcon={<Refresh />}
            onClick={loadThreats}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={2} mb={3}>
        <Grid item xs={12} sm={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom variant="body2">
                Total Threats
              </Typography>
              <Typography variant="h4">
                {threats.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom variant="body2">
                Critical Threats
              </Typography>
              <Typography variant="h4" color="error.main">
                {threats.filter(t => t.severity === 'CRITICAL').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom variant="body2">
                Blocked
              </Typography>
              <Typography variant="h4" color="success.main">
                {threats.filter(t => t.action_taken === 'BLOCKED').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom variant="body2">
                False Positives
              </Typography>
              <Typography variant="h4" color="warning.main">
                {threats.filter(t => t.is_false_positive).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Threats Table */}
      <Card>
        <CardContent>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Threat</TableCell>
                  <TableCell>Severity</TableCell>
                  <TableCell>Source</TableCell>
                  <TableCell>Target</TableCell>
                  <TableCell>Confidence</TableCell>
                  <TableCell>Action</TableCell>
                  <TableCell>Service</TableCell>
                  <TableCell>Time</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {threats.map((threat) => (
                  <TableRow 
                    key={threat.id}
                    sx={{
                      backgroundColor: threat.is_false_positive ? 
                        theme.palette.grey[50] : 'inherit'
                    }}
                  >
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={1}>
                        {threat.is_false_positive && (
                          <Tooltip title="False Positive">
                            <Flag color="warning" fontSize="small" />
                          </Tooltip>
                        )}
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {threat.threat_category.replace('_', ' ')}
                          </Typography>
                          {threat.attack_signature && (
                            <Typography variant="caption" color="textSecondary">
                              {threat.attack_signature.substring(0, 50)}...
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={threat.severity}
                        size="small"
                        sx={{
                          backgroundColor: getSeverityColor(threat.severity),
                          color: 'white'
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {threat.source_ip}
                        {threat.source_port && `:${threat.source_port}`}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {threat.target_ip}
                        {threat.target_port && `:${threat.target_port}`}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={1}>
                        <LinearProgress
                          variant="determinate"
                          value={threat.confidence_score}
                          sx={{ width: 60, height: 6 }}
                        />
                        <Typography variant="caption">
                          {threat.confidence_score.toFixed(0)}%
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={1}>
                        {getActionIcon(threat.action_taken)}
                        <Typography variant="body2">
                          {threat.action_taken.replace('_', ' ')}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Tooltip title={threat.detecting_service.service_type}>
                        <Chip
                          label={threat.detecting_service.name}
                          size="small"
                          variant="outlined"
                        />
                      </Tooltip>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(threat.created_at).toLocaleString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => openThreatDetail(threat)}
                        >
                          <Visibility />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Filter Dialog */}
      <Dialog open={filterDialogOpen} onClose={() => setFilterDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Filter Threats</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Threat Category</InputLabel>
                <Select
                  value={filters.threat_category}
                  onChange={(e) => handleFilterChange('threat_category', e.target.value)}
                  label="Threat Category"
                >
                  <MenuItem value="">All Categories</MenuItem>
                  {THREAT_CATEGORIES.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category.replace('_', ' ')}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Severity</InputLabel>
                <Select
                  value={filters.severity}
                  onChange={(e) => handleFilterChange('severity', e.target.value)}
                  label="Severity"
                >
                  <MenuItem value="">All Severities</MenuItem>
                  {SEVERITY_LEVELS.map((severity) => (
                    <MenuItem key={severity} value={severity}>
                      {severity}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Action Taken</InputLabel>
                <Select
                  value={filters.action_taken}
                  onChange={(e) => handleFilterChange('action_taken', e.target.value)}
                  label="Action Taken"
                >
                  <MenuItem value="">All Actions</MenuItem>
                  {ACTION_TYPES.map((action) => (
                    <MenuItem key={action} value={action}>
                      {action.replace('_', ' ')}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>False Positive</InputLabel>
                <Select
                  value={filters.is_false_positive}
                  onChange={(e) => handleFilterChange('is_false_positive', e.target.value)}
                  label="False Positive"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="true">False Positives Only</MenuItem>
                  <MenuItem value="false">Exclude False Positives</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Date From"
                type="datetime-local"
                value={filters.date_from}
                onChange={(e) => handleFilterChange('date_from', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Date To"
                type="datetime-local"
                value={filters.date_to}
                onChange={(e) => handleFilterChange('date_to', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={clearFilters}>Clear All</Button>
          <Button onClick={() => setFilterDialogOpen(false)}>Cancel</Button>
          <Button onClick={() => setFilterDialogOpen(false)} variant="contained">
            Apply Filters
          </Button>
        </DialogActions>
      </Dialog>

      {/* Threat Detail Dialog */}
      <Dialog
        open={detailDialogOpen}
        onClose={() => setDetailDialogOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Threat Details
        </DialogTitle>
        <DialogContent>
          {selectedThreat && (
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="textSecondary">Threat Category</Typography>
                <Typography variant="body1">{selectedThreat.threat_category.replace('_', ' ')}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="textSecondary">Severity</Typography>
                <Chip
                  label={selectedThreat.severity}
                  size="small"
                  sx={{
                    backgroundColor: getSeverityColor(selectedThreat.severity),
                    color: 'white'
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="textSecondary">Source IP</Typography>
                <Typography variant="body1">{selectedThreat.source_ip}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="textSecondary">Target IP</Typography>
                <Typography variant="body1">{selectedThreat.target_ip}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="textSecondary">Confidence Score</Typography>
                <Typography variant="body1">{selectedThreat.confidence_score.toFixed(2)}%</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="textSecondary">Action Taken</Typography>
                <Typography variant="body1">{selectedThreat.action_taken.replace('_', ' ')}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="textSecondary">Detecting Service</Typography>
                <Typography variant="body1">{selectedThreat.detecting_service.name}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="textSecondary">Detection Time</Typography>
                <Typography variant="body1">{new Date(selectedThreat.created_at).toLocaleString()}</Typography>
              </Grid>
              {selectedThreat.attack_signature && (
                <Grid item xs={12}>
                  <Typography variant="body2" color="textSecondary">Attack Signature</Typography>
                  <Typography variant="body1" sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                    {selectedThreat.attack_signature}
                  </Typography>
                </Grid>
              )}
              {selectedThreat.geolocation && Object.keys(selectedThreat.geolocation).length > 0 && (
                <Grid item xs={12}>
                  <Typography variant="body2" color="textSecondary">Geolocation</Typography>
                  <Typography variant="body1">
                    {JSON.stringify(selectedThreat.geolocation, null, 2)}
                  </Typography>
                </Grid>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ThreatDetectionComponent;
