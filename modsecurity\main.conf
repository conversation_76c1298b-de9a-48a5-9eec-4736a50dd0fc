# ModSecurity Advanced Configuration - TrustVault Enterprise
# Paranoia Level 4 - Maximum Security

SecRuleEngine On
SecRequestBodyAccess On
SecResponseBodyAccess On
SecRequestBodyLimit 13107200
SecRequestBodyNoFilesLimit 131072
SecRequestBodyInMemoryLimit 131072
SecRequestBodyLimitAction Reject
SecPcreMatchLimit 5000
SecPcreMatchLimitRecursion 5000

# Advanced Audit Configuration
SecAuditEngine RelevantOnly
SecAuditLogRelevantStatus "^(?:5|4(?!04))"
SecAuditLogParts ABIJDEFHZ
SecAuditLogType Serial
SecAuditLog /var/log/modsec_audit.log
SecAuditLogStorageDir /var/log/modsec_audit/

# Advanced Anomaly Scoring
SecDefaultAction "phase:1,log,auditlog,pass,tag:'Local Lab Rules'"
SecDefaultAction "phase:2,log,auditlog,pass,tag:'Local Lab Rules'"

# OWASP Core Rule Set with Maximum Paranoia
Include /etc/modsecurity/owasp-crs/crs-setup.conf
Include /etc/modsecurity/owasp-crs/rules/*.conf

# Advanced Custom TrustVault Rules
# SQL Injection - Advanced Detection
SecRule ARGS "@detectSQLi" \
    "id:2001,\
    phase:2,\
    block,\
    msg:'Advanced SQL Injection Attack Detected',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-sqli',\
    tag:'OWASP_CRS',\
    tag:'capec/1000/152/248/66',\
    ver:'OWASP_CRS/4.0.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
    setvar:'tx.sql_injection_score=+1'"

# XSS - Advanced Detection with Context Analysis
SecRule ARGS "@detectXSS" \
    "id:2002,\
    phase:2,\
    block,\
    msg:'Advanced XSS Attack Detected',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-xss',\
    tag:'OWASP_CRS',\
    tag:'capec/1000/152/242/63',\
    ver:'OWASP_CRS/4.0.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
    setvar:'tx.xss_score=+1'"

# Advanced Path Traversal Detection
SecRule REQUEST_FILENAME "@contains ../" \
    "id:2003,\
    phase:2,\
    block,\
    msg:'Advanced Path Traversal Attack Detected',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-lfi',\
    tag:'OWASP_CRS',\
    tag:'capec/1000/255/153/126',\
    ver:'OWASP_CRS/4.0.0',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}',\
    setvar:'tx.lfi_score=+1'"

# Command Injection Detection
SecRule ARGS "@detectCmdInjection" \
    "id:2004,\
    phase:2,\
    block,\
    msg:'Command Injection Attack Detected',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
    tag:'application-multi',\
    tag:'language-multi',\
    tag:'platform-multi',\
    tag:'attack-injection-generic',\
    severity:'CRITICAL',\
    setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"

# Advanced Rate Limiting with IP Reputation
SecRule IP:REQUEST_COUNT "@gt 50" \
    "id:2005,\
    phase:1,\
    block,\
    msg:'Rate limit exceeded - Potential DDoS',\
    expirevar:IP.REQUEST_COUNT=60,\
    setvar:'tx.anomaly_score_pl1=+%{tx.warning_anomaly_score}'"

SecAction "id:2006,phase:1,pass,initcol:IP=%{REMOTE_ADDR},setvar:IP.REQUEST_COUNT=+1"

# Financial Application Specific Rules
# Credit Card Number Detection
SecRule ARGS "@detectCreditCard" \
    "id:2007,\
    phase:2,\
    block,\
    msg:'Credit Card Number Detected in Request',\
    logdata:'Potential PCI DSS Violation',\
    tag:'application-financial',\
    tag:'pci-dss',\
    severity:'CRITICAL'"

# SSN Detection
SecRule ARGS "@rx (?!000|666|9\d{2})\d{3}-(?!00)\d{2}-(?!0000)\d{4}" \
    "id:2008,\
    phase:2,\
    block,\
    msg:'Social Security Number Detected',\
    logdata:'Potential PII Violation',\
    tag:'application-financial',\
    tag:'pii-protection',\
    severity:'CRITICAL'"

# Advanced Session Hijacking Protection
SecRule REQUEST_COOKIES "@rx JSESSIONID=([A-F0-9]{32})" \
    "id:2009,\
    phase:1,\
    pass,\
    msg:'Session Token Validation',\
    setvar:'tx.session_token=%{MATCHED_VAR}'"

# Malicious User Agent Detection
SecRule REQUEST_HEADERS:User-Agent "@pmFromFile /etc/modsecurity/malicious-ua.txt" \
    "id:2010,\
    phase:1,\
    block,\
    msg:'Malicious User Agent Detected',\
    tag:'attack-reputation-scanner',\
    severity:'WARNING'"

