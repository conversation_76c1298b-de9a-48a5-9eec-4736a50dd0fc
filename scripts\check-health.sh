#!/bin/bash

# TrustVault - Health Check Script
# This script checks the health of all services

echo "🛡️ TrustVault - Service Health Check"
echo "===================================="

# Function to check service health
check_service() {
    local service=$1
    local container=$2
    
    echo -n "Checking $service... "
    
    if docker-compose ps $container | grep -q "Up"; then
        echo "✅ Running"
        return 0
    else
        echo "❌ Not running"
        return 1
    fi
}

# Function to check service logs for errors
check_logs() {
    local service=$1
    local container=$2
    
    echo "📋 Recent logs for $service:"
    docker-compose logs --tail=5 $container
    echo ""
}

# Check all services
echo "🔍 Checking service status..."
echo ""

check_service "PostgreSQL" "postgres"
check_service "Redis" "redis"
check_service "Django" "django"
check_service "Celery Worker" "celery-worker"
check_service "Celery Beat" "celery-beat"
check_service "Nginx" "nginx"
check_service "React Frontend" "react"

echo ""
echo "📊 Container Status:"
docker-compose ps

echo ""
echo "🔍 Checking for common issues..."

# Check Django logs for specific errors
echo "🔍 Checking Django for ALLOWED_HOSTS errors..."
if docker-compose logs django 2>/dev/null | grep -q "Invalid HTTP_HOST header"; then
    echo "❌ Found ALLOWED_HOSTS errors in Django logs"
else
    echo "✅ No ALLOWED_HOSTS errors found"
fi

# Check Celery logs for app loading errors
echo "🔍 Checking Celery for app loading errors..."
if docker-compose logs celery-worker 2>/dev/null | grep -q "Module 'trustvault' has no attribute 'celery'"; then
    echo "❌ Found Celery app loading errors"
else
    echo "✅ No Celery app loading errors found"
fi

# Check PostgreSQL for database errors
echo "🔍 Checking PostgreSQL for database errors..."
if docker-compose logs postgres 2>/dev/null | grep -q "database.*does not exist"; then
    echo "❌ Found database existence errors"
else
    echo "✅ No database existence errors found"
fi

# Check memory issues
echo "🔍 Checking for memory allocation errors..."
if docker-compose logs django celery-worker celery-beat 2>/dev/null | grep -q "Cannot allocate memory"; then
    echo "❌ Found memory allocation errors"
else
    echo "✅ No memory allocation errors found"
fi

echo ""
echo "✅ Health check completed!"
