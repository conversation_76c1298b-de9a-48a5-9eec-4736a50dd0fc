# TrustVault Advanced IDS Rules - Enterprise Grade
# Financial Application Specific Detection

# Advanced SQL Injection Detection
alert http any any -> any any (msg:"TrustVault: Advanced SQL Injection Attempt"; flow:established,to_server; content:"POST"; http_method; content:"application/x-www-form-urlencoded"; http_header; pcre:"/(\%27)|(\')|(\-\-)|(\%23)|(#)/i"; pcre:"/(((\%3D)|(=))[^\n]*((\%27)|(\')|(\-\-)|(\%23)|(#)))/i"; classtype:web-application-attack; sid:3000001; rev:1;)

alert http any any -> any any (msg:"TrustVault: SQL Injection UNION Attack"; flow:established,to_server; content:"union"; nocase; content:"select"; nocase; distance:0; within:100; classtype:web-application-attack; sid:3000002; rev:1;)

alert http any any -> any any (msg:"TrustVault: SQL Injection Information Schema"; flow:established,to_server; content:"information_schema"; nocase; classtype:web-application-attack; sid:3000003; rev:1;)

# Advanced XSS Detection
alert http any any -> any any (msg:"TrustVault: Advanced XSS Attack"; flow:established,to_server; content:"<script"; nocase; pcre:"/\x3cscript[^\x3e]*\x3e.*?\x3c\/script\x3e/is"; classtype:web-application-attack; sid:3000004; rev:1;)

alert http any any -> any any (msg:"TrustVault: XSS Event Handler"; flow:established,to_server; pcre:"/(on\w+\s*=)/i"; classtype:web-application-attack; sid:3000005; rev:1;)

alert http any any -> any any (msg:"TrustVault: XSS JavaScript Protocol"; flow:established,to_server; content:"javascript:"; nocase; classtype:web-application-attack; sid:3000006; rev:1;)

# Command Injection Detection
alert http any any -> any any (msg:"TrustVault: Command Injection Attempt"; flow:established,to_server; pcre:"/(\||;|&|\$\(|\`)/"; classtype:web-application-attack; sid:3000007; rev:1;)

alert http any any -> any any (msg:"TrustVault: System Command Execution"; flow:established,to_server; pcre:"/(wget|curl|nc|netcat|bash|sh|cmd|powershell)/i"; classtype:web-application-attack; sid:3000008; rev:1;)

# Path Traversal Detection
alert http any any -> any any (msg:"TrustVault: Advanced Path Traversal"; flow:established,to_server; pcre:"/(\.\.[\/\\]){3,}/"; classtype:web-application-attack; sid:3000009; rev:1;)

alert http any any -> any any (msg:"TrustVault: Windows Path Traversal"; flow:established,to_server; content:"..\\"; classtype:web-application-attack; sid:3000010; rev:1;)

# File Inclusion Attacks
alert http any any -> any any (msg:"TrustVault: Local File Inclusion"; flow:established,to_server; pcre:"/(\/etc\/passwd|\/etc\/shadow|\/proc\/self\/environ)/i"; classtype:web-application-attack; sid:3000011; rev:1;)

alert http any any -> any any (msg:"TrustVault: Remote File Inclusion"; flow:established,to_server; pcre:"/((http|https|ftp):\/\/[^\/]*\/)/i"; classtype:web-application-attack; sid:3000012; rev:1;)

# Financial Data Protection
alert http any any -> any any (msg:"TrustVault: Credit Card Number Detected"; flow:established,to_server; pcre:"/\b(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|3[47][0-9]{13}|3[0-9]{13}|6(?:011|5[0-9]{2})[0-9]{12})\b/"; classtype:policy-violation; sid:3000013; rev:1;)

alert http any any -> any any (msg:"TrustVault: SSN Pattern Detected"; flow:established,to_server; pcre:"/\b(?!000|666|9\d{2})\d{3}-(?!00)\d{2}-(?!0000)\d{4}\b/"; classtype:policy-violation; sid:3000014; rev:1;)

alert http any any -> any any (msg:"TrustVault: Bank Account Pattern"; flow:established,to_server; pcre:"/\b\d{8,17}\b/"; content:"account"; nocase; distance:0; within:50; classtype:policy-violation; sid:3000015; rev:1;)

# Advanced Scanning Detection
alert tcp any any -> any any (msg:"TrustVault: Port Scan Detected"; flags:S,12; detection_filter:track by_src, count 15, seconds 60; classtype:attempted-recon; sid:3000016; rev:1;)

alert tcp any any -> any any (msg:"TrustVault: Stealth Scan Detected"; flags:F; detection_filter:track by_src, count 10, seconds 60; classtype:attempted-recon; sid:3000017; rev:1;)

alert tcp any any -> any any (msg:"TrustVault: NULL Scan Detected"; flags:0; detection_filter:track by_src, count 10, seconds 60; classtype:attempted-recon; sid:3000018; rev:1;)

# Brute Force Detection
alert http any any -> any any (msg:"TrustVault: Brute Force Login Attempt"; flow:established,to_server; content:"POST"; http_method; content:"/login"; http_uri; detection_filter:track by_src, count 10, seconds 60; classtype:attempted-user; sid:3000019; rev:1;)

alert http any any -> any any (msg:"TrustVault: Admin Panel Brute Force"; flow:established,to_server; content:"POST"; http_method; content:"/admin"; http_uri; detection_filter:track by_src, count 5, seconds 60; classtype:attempted-user; sid:3000020; rev:1;)

# Malicious User Agents
alert http any any -> any any (msg:"TrustVault: Malicious Scanner User Agent"; flow:established,to_server; content:"User-Agent|3a 20|"; http_header; pcre:"/User-Agent\x3a\x20.*(sqlmap|nikto|nmap|masscan|acunetix|nessus|openvas|w3af|skipfish|dirb|gobuster|wfuzz|hydra|medusa)/i"; classtype:web-application-attack; sid:3000021; rev:1;)

# Advanced Evasion Detection
alert http any any -> any any (msg:"TrustVault: HTTP Parameter Pollution"; flow:established,to_server; pcre:"/[?&]([^=]+)=[^&]*&\1=/"; classtype:web-application-attack; sid:3000022; rev:1;)

alert http any any -> any any (msg:"TrustVault: Double URL Encoding"; flow:established,to_server; pcre:"/%25[0-9a-fA-F]{2}/"; classtype:web-application-attack; sid:3000023; rev:1;)

# Session Attacks
alert http any any -> any any (msg:"TrustVault: Session Fixation Attempt"; flow:established,to_server; content:"Set-Cookie"; http_header; content:"JSESSIONID"; http_header; classtype:web-application-attack; sid:3000024; rev:1;)

alert http any any -> any any (msg:"TrustVault: CSRF Attack Attempt"; flow:established,to_server; content:"POST"; http_method; content:"Referer"; http_header; pcre:"/Referer\x3a\x20(?!https?\x3a\/\/[^\/]*trustvault)/i"; classtype:web-application-attack; sid:3000025; rev:1;)

# Advanced Payload Detection
alert http any any -> any any (msg:"TrustVault: Encoded Payload Detection"; flow:established,to_server; pcre:"/(%[0-9a-fA-F]{2}){10,}/"; classtype:web-application-attack; sid:3000026; rev:1;)

alert http any any -> any any (msg:"TrustVault: Base64 Encoded Payload"; flow:established,to_server; pcre:"/[A-Za-z0-9+\/]{20,}={0,2}/"; classtype:web-application-attack; sid:3000027; rev:1;)

# DDoS Detection
alert tcp any any -> any 80 (msg:"TrustVault: HTTP Flood Attack"; flow:established,to_server; content:"GET"; http_method; detection_filter:track by_src, count 100, seconds 10; classtype:attempted-dos; sid:3000028; rev:1;)

alert tcp any any -> any 443 (msg:"TrustVault: HTTPS Flood Attack"; flow:established,to_server; detection_filter:track by_src, count 100, seconds 10; classtype:attempted-dos; sid:3000029; rev:1;)

# Advanced Malware Detection
alert http any any -> any any (msg:"TrustVault: Suspicious File Upload"; flow:established,to_server; content:"POST"; http_method; content:"multipart/form-data"; http_header; pcre:"/filename=.*\.(php|jsp|asp|aspx|exe|bat|cmd|sh)/i"; classtype:web-application-attack; sid:3000030; rev:1;)

