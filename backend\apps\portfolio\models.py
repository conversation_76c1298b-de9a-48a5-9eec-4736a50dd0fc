# TrustVault - Portfolio Models

from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal
from apps.core.models import BaseModel

User = get_user_model()


class Portfolio(BaseModel):
    """Portfolio model for managing investment portfolios."""
    
    PORTFOLIO_TYPES = [
        ('CONSERVATIVE', 'Conservative'),
        ('MODERATE', 'Moderate'),
        ('AGGRESSIVE', 'Aggressive'),
        ('CUSTOM', 'Custom'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='portfolios')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    portfolio_type = models.CharField(max_length=20, choices=PORTFOLIO_TYPES, default='MODERATE')
    total_value = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    currency = models.CharField(max_length=3, default='USD')
    is_public = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'portfolio_portfolio'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['portfolio_type']),
            models.Index(fields=['is_public']),
        ]
    
    def __str__(self):
        # Match expected string representation in tests files ("Name (email)")
        return f"{self.name} ({self.user.email})"

    def calculate_total_value(self):
        """Calculate total portfolio value."""
        total = sum(holding.current_value for holding in self.holdings.all())
        self.total_value = total
        self.save(update_fields=['total_value'])
        return total


class Asset(BaseModel):
    """Asset model for different investment types."""
    
    ASSET_TYPES = [
        ('STOCK', 'Stock'),
        ('BOND', 'Bond'),
        ('ETF', 'ETF'),
        ('MUTUAL_FUND', 'Mutual Fund'),
        ('CRYPTO', 'Cryptocurrency'),
        ('COMMODITY', 'Commodity'),
        ('REAL_ESTATE', 'Real Estate'),
        ('CASH', 'Cash'),
    ]
    
    symbol = models.CharField(max_length=20, unique=True)
    name = models.CharField(max_length=200)
    asset_type = models.CharField(max_length=20, choices=ASSET_TYPES)
    description = models.TextField(blank=True)
    current_price = models.DecimalField(max_digits=15, decimal_places=4)
    currency = models.CharField(max_length=3, default='USD')
    
    # Market data
    market_cap = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    volume_24h = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    change_24h = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True)
    
    # Metadata
    sector = models.CharField(max_length=100, blank=True)
    country = models.CharField(max_length=100, blank=True)
    
    class Meta:
        db_table = 'portfolio_asset'
        ordering = ['symbol']
        indexes = [
            models.Index(fields=['symbol']),
            models.Index(fields=['asset_type']),
            models.Index(fields=['sector']),
        ]
    
    def __str__(self):
        return f"{self.symbol} - {self.name}"

    def save(self, *args, **kwargs):
        """Override save to ensure current_price is Decimal and update holdings."""
        if isinstance(self.current_price, str):
            self.current_price = Decimal(self.current_price)
        super().save(*args, **kwargs)

        # Update all holdings for this asset when price changes
        if 'current_price' in kwargs.get('update_fields', []) or not kwargs.get('update_fields'):
            # Use the correct reverse relationship name
            holdings = getattr(self, 'holdings', None)
            if holdings:
                for holding in holdings.all():
                    holding.calculate_current_value()


class Holding(BaseModel):
    """Holding model for portfolio positions."""
    
    portfolio = models.ForeignKey(Portfolio, on_delete=models.CASCADE, related_name='holdings')
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='holdings')
    quantity = models.DecimalField(max_digits=20, decimal_places=8)
    average_cost = models.DecimalField(max_digits=15, decimal_places=4)
    current_value = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))

    @property
    def total_cost(self):
        return self.quantity * self.average_cost

    class Meta:
        db_table = 'portfolio_holding'
        unique_together = ['portfolio', 'asset']
        indexes = [
            models.Index(fields=['portfolio', 'asset']),
            models.Index(fields=['current_value']),
        ]
    
    def __str__(self):
        return f"{self.portfolio.name} - {self.asset.symbol} ({self.quantity})"
    
    def calculate_current_value(self):
        """Calculate current value of holding."""
        # Ensure current_price is Decimal
        price = self.asset.current_price
        if isinstance(price, str):
            price = Decimal(price)
        self.current_value = self.quantity * price
        self.save(update_fields=['current_value'])
        return self.current_value
    
    @property
    def profit_loss(self):
        """Calculate profit/loss for this holding."""
        cost_basis = self.quantity * self.average_cost
        return self.current_value - cost_basis
    
    @property
    def profit_loss_percentage(self):
        """Calculate profit/loss percentage."""
        cost_basis = self.quantity * self.average_cost
        if cost_basis > 0:
            return ((self.current_value - cost_basis) / cost_basis) * 100
        return Decimal('0.00')


class Transaction(BaseModel):
    """Transaction model for portfolio activities."""
    
    TRANSACTION_TYPES = [
        ('BUY', 'Buy'),
        ('SELL', 'Sell'),
        ('DIVIDEND', 'Dividend'),
        ('SPLIT', 'Stock Split'),
        ('DEPOSIT', 'Cash Deposit'),
        ('WITHDRAWAL', 'Cash Withdrawal'),
    ]
    
    portfolio = models.ForeignKey(Portfolio, on_delete=models.CASCADE, related_name='transactions')
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='transactions', null=True, blank=True)
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    quantity = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    price = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    fees = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    notes = models.TextField(blank=True)
    transaction_date = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        """Override save to calculate total_amount if not provided."""
        if not self.total_amount and self.quantity and self.price:
            self.total_amount = self.quantity * self.price
        super().save(*args, **kwargs)

    class Meta:
        db_table = 'portfolio_transaction'
        ordering = ['-transaction_date']
        indexes = [
            models.Index(fields=['portfolio', 'transaction_date']),
            models.Index(fields=['transaction_type', 'transaction_date']),
            models.Index(fields=['asset', 'transaction_date']),
        ]
    
    def save(self, *args, **kwargs):
        """Override save to calculate total_amount if not provided."""
        if not self.total_amount and self.quantity and self.price:
            self.total_amount = self.quantity * self.price
        elif not self.total_amount:
            # For cash transactions without quantity/price
            self.total_amount = Decimal('0.00')
        super().save(*args, **kwargs)

    def __str__(self):
        # Tests expect compact string like: "BUY 10.00 AAPL @ $145.00"
        if self.transaction_type in ['BUY', 'SELL'] and self.asset and self.quantity and self.price:
            qty = f"{self.quantity:.2f}"
            price = f"${self.price:.2f}"
            return f"{self.transaction_type} {qty} {self.asset.symbol} @ {price}"
        return f"{self.portfolio.name} - {self.transaction_type} - {self.transaction_date}"


class Watchlist(BaseModel):
    """Watchlist model for tracking assets of interest."""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='watchlists')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    assets = models.ManyToManyField(Asset, through='WatchlistItem')
    
    class Meta:
        db_table = 'portfolio_watchlist'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.name}"


class WatchlistItem(BaseModel):
    """Watchlist item model for many-to-many relationship."""
    
    watchlist = models.ForeignKey(Watchlist, on_delete=models.CASCADE)
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE)
    target_price = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    notes = models.TextField(blank=True)
    
    class Meta:
        db_table = 'portfolio_watchlist_item'
        unique_together = ['watchlist', 'asset']
    
    def __str__(self):
        return f"{self.watchlist.name} - {self.asset.symbol}"


class PortfolioSnapshot(BaseModel):
    """Portfolio snapshot model for historical tracking."""
    
    portfolio = models.ForeignKey(Portfolio, on_delete=models.CASCADE, related_name='snapshots')
    total_value = models.DecimalField(max_digits=15, decimal_places=2)
    snapshot_data = models.JSONField()  # Store holdings data
    snapshot_date = models.DateTimeField()
    
    class Meta:
        db_table = 'portfolio_snapshot'
        ordering = ['-snapshot_date']
        indexes = [
            models.Index(fields=['portfolio', 'snapshot_date']),
        ]
    
    def __str__(self):
        return f"{self.portfolio.name} - {self.snapshot_date}"
